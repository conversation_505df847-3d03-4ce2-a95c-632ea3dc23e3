# Electron PDF 循环播放器

这是一个用于自动、全屏、循环播放指定文件夹内 PDF 文件的 Electron 应用。它被设计为在无人值守的情况下运行，非常适合在展示屏幕或信息亭上使用。

## 功能特性

*   **自动读取路径**: 自动从 [`config.json`](config.json) 配置文件中读取存放 PDF 文件的文件夹路径。
*   **窗口化显示**: 应用默认以窗口模式启动。
*   **自动滚动**: 自动平滑地滚动播放单个 PDF 文件的所有页面。
*   **循环播放**: 当一个 PDF 文件播放完毕后，自动加载并播放文件夹中的下一个 PDF 文件，实现无缝循环。
*   **无需用户交互**: 整个过程完全自动化，启动后无需任何手动操作。

## 安装与运行

### 安装依赖

在项目根目录下运行以下命令来安装所需依赖：

```bash
npm install
```

### 运行应用

安装完依赖后，使用以下命令启动应用：

```bash
npm start
```

## 配置

在运行应用之前，您需要配置 PDF 文件所在的文件夹路径。

1.  打开项目根目录下的 [`config.json`](config.json) 文件。
2.  修改 `pdfFolderPath` 的值，指向您存放 PDF 文件的文件夹。

**配置示例:**

```json
{
  "pdfFolderPath": "C:/Users/<USER>/Documents/PDFs"
}
```

## 故障排查

如果遇到问题（例如，PDF 未显示或应用显示错误消息），您可以打开开发者工具来查看详细的控制台日志。

*   在应用窗口中，按下 `Ctrl+Shift+I` (Windows/Linux) 或 `Cmd+Option+I` (macOS) 来打开开发者工具。
*   切换到 "Console" 标签页，查看由主进程 (`main.js`) 和渲染器进程 (`renderer.js`) 输出的日志信息。
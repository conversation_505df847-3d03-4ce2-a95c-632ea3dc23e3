# WpfPdfViewer

一个简单的使用 C# 和 WPF 构建的桌面应用程序，用于查看 PDF 文件。

## 功能

*   从本地文件系统打开 PDF 文件。
*   逐页查看 PDF 内容。
*   通过“上一页”和“下一页”按钮进行页面导航。
*   显示当前页码和总页数。

## 依赖项

该项目使用以下 NuGet 包：

*   [PdfiumViewer.WPF](https://www.nuget.org/packages/PdfiumViewer.WPF/)

## 如何构建和运行

1.  **克隆或下载此仓库。**
2.  **打开终端或命令提示符，并导航到 `WpfPdfViewer` 项目目录。**
3.  **构建项目：**
    ```bash
    dotnet build
    ```
    要创建一个 Release 构建，请使用 `--configuration Release` 标志：
    ```bash
    dotnet build --configuration Release
    ```
4.  **运行项目：**
    ```bash
    dotnet run
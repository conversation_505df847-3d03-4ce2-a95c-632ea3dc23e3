using Newtonsoft.Json;
using System.IO;

namespace WpfPdfViewer.Services
{
    public class ConfigService
    {
        private class ConfigModel
        {
            public string PdfFolderPath { get; set; } = "pdfs/";
        }

        public string GetPdfFolderPath()
        {
            var configPath = Path.Combine(AppContext.BaseDirectory, "config.json");
            if (File.Exists(configPath))
            {
                var json = File.ReadAllText(configPath);
                var config = JsonConvert.DeserializeObject<ConfigModel>(json);
                if (config != null && !string.IsNullOrWhiteSpace(config.PdfFolderPath))
                {
                    return config.PdfFolderPath;
                }
            }
            return "pdfs/"; // Default value
        }
    }
}
using System;
using System.Drawing;
using System.Drawing.Imaging;
using System.IO;
using System.Windows.Media.Imaging;
using PdfiumViewer;
using System.Collections.Generic;
using System.Linq;
using System.Diagnostics;

namespace WpfPdfViewer.Services
{
    public class PdfService : IDisposable
    {
        private PdfDocument? _pdfDocument;

        public int PageCount => _pdfDocument?.PageCount ?? 0;

        public IEnumerable<string> GetPdfFiles(string directoryPath)
        {
            if (!Directory.Exists(directoryPath))
            {
                return Enumerable.Empty<string>();
            }
            return Directory.EnumerateFiles(directoryPath, "*.pdf");
        }

        public bool LoadPdf(string filePath)
        {
            try
            {
                if (_pdfDocument != null)
                {
                    _pdfDocument.Dispose();
                }
                _pdfDocument = PdfDocument.Load(filePath);
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] Failed to load PDF: {filePath}. Reason: {ex.Message}");
                _pdfDocument = null;
                return false;
            }
        }

        public List<BitmapSource> RenderAllPages(string filePath)
        {
            var pages = new List<BitmapSource>();
            try
            {
                using (var document = PdfDocument.Load(filePath))
                {
                    for (int i = 0; i < document.PageCount; i++)
                    {
                        try
                        {
                            var page = RenderPage(document, i);
                            if (page != null)
                            {
                                pages.Add(page);
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"[ERROR] Failed to render page {i} from PDF: {filePath}. Reason: {ex.Message}");
                            // Continue to next page
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"[ERROR] Failed to load PDF for rendering all pages: {filePath}. Reason: {ex.Message}");
            }
            return pages;
        }

        private BitmapSource? RenderPage(PdfDocument document, int pageNumber)
        {
            if (document == null || pageNumber < 0 || pageNumber >= document.PageCount)
            {
                return null;
            }

            // Using a fixed width and rendering based on aspect ratio
            var page = document.Render(pageNumber, 1024, 0, true);

            using (var stream = new MemoryStream())
            {
                page.Save(stream, ImageFormat.Png);
                var bitmapImage = new BitmapImage();
                bitmapImage.BeginInit();
                bitmapImage.CacheOption = BitmapCacheOption.OnLoad;
                bitmapImage.StreamSource = stream;
                bitmapImage.EndInit();
                bitmapImage.Freeze(); // Important for use in different threads
                return bitmapImage;
            }
        }

        public BitmapSource? RenderPage(int pageNumber)
        {
            if (_pdfDocument == null) return null;
            return RenderPage(_pdfDocument, pageNumber);
        }

        public void Dispose()
        {
            _pdfDocument?.Dispose();
        }
    }
}
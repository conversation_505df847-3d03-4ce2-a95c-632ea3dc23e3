using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Windows.Media.Imaging;
using WpfPdfViewer.Services;

namespace WpfPdfViewer.ViewModels
{
    public class MainViewModel : INotifyPropertyChanged, IDisposable
    {
        private readonly ConfigService _configService;
        private readonly PdfService _pdfService;
        private List<string> _pdfFiles = new List<string>();
        private int _currentFileIndex = -1;

        public ObservableCollection<BitmapSource> PdfPages { get; } = new ObservableCollection<BitmapSource>();

        public MainViewModel()
        {
            _configService = new ConfigService();
            _pdfService = new PdfService();

            LoadPdfFilesList();

        }

        public void Initialize()
        {
            AdvanceToNextDocument();
        }

        private void LoadPdfFilesList()
        {
            var pdfFolderPath = _configService.GetPdfFolderPath();
            _pdfFiles = _pdfService.GetPdfFiles(pdfFolderPath).ToList();
        }

        private void LoadPdfDocument()
        {
            if (_currentFileIndex >= 0 && _currentFileIndex < _pdfFiles.Count)
            {
                var filePath = _pdfFiles[_currentFileIndex];
                var pages = _pdfService.RenderAllPages(filePath);

                PdfPages.Clear();
                foreach (var page in pages)
                {
                    PdfPages.Add(page);
                }
            }
        }

        public void AdvanceToNextDocument()
        {
            _currentFileIndex++;
            if (_currentFileIndex >= _pdfFiles.Count)
            {
                _currentFileIndex = 0; // Loop back to the start
            }

            if (!_pdfFiles.Any())
            {
                PdfPages.Clear();
                return;
            }

            LoadPdfDocument();
        }

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }



        public void Dispose()
        {
            _pdfService.Dispose();
        }
    }
}
<Window x:Class="WpfPdfViewer.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:WpfPdfViewer.Views"
        mc:Ignorable="d"
        Title="WPF PDF Viewer" Height="800" Width="600"
>
    <Grid>
        <ScrollViewer x:Name="PdfScroller"
                      VerticalScrollBarVisibility="Hidden"
                      HorizontalScrollBarVisibility="Disabled"
                      ScrollChanged="PdfScroller_ScrollChanged">
            <ItemsControl ItemsSource="{Binding PdfPages}">
                <ItemsControl.ItemTemplate>
                    <DataTemplate>
                        <Image Source="{Binding}" Margin="5" />
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
            </ItemsControl>
        </ScrollViewer>
    </Grid>
</Window>
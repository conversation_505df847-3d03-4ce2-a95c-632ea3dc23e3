using System;
using System.Collections.Specialized;
using System.ComponentModel;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Threading;
using WpfPdfViewer.ViewModels;

namespace WpfPdfViewer.Views
{
    public partial class MainWindow : Window
    {
        private readonly MainViewModel _viewModel;
        private readonly DispatcherTimer _scrollTimer;

        public MainWindow()
        {
            InitializeComponent();
            _viewModel = new MainViewModel();
            DataContext = _viewModel;

            _scrollTimer = new DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(20) // Controls scroll speed
            };
            _scrollTimer.Tick += ScrollTimer_Tick;

            if (_viewModel.PdfPages.Any())
            {
                _scrollTimer.Start();
            }

            // Listen for changes to the collection to reset scroll and restart timer
            if (_viewModel.PdfPages is INotifyCollectionChanged collection)
            {
                collection.CollectionChanged += PdfPages_CollectionChanged;
            }
            Loaded += MainWindow_Loaded;
        }

        private void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            // Ensure this runs only once
            Loaded -= MainWindow_Loaded;

            // Now that the view is ready, initialize the ViewModel
            _viewModel.Initialize();
        }

        private void ScrollTimer_Tick(object? sender, EventArgs e)
        {
            PdfScroller.ScrollToVerticalOffset(PdfScroller.VerticalOffset + 1);
        }

        private void PdfScroller_ScrollChanged(object sender, ScrollChangedEventArgs e)
        {
            // Check if scrolled to the bottom
            if (e.VerticalOffset >= PdfScroller.ScrollableHeight && PdfScroller.ScrollableHeight > 0)
            {
                _scrollTimer.Stop();
                _viewModel.AdvanceToNextDocument();
            }
        }

        private void PdfPages_CollectionChanged(object? sender, NotifyCollectionChangedEventArgs e)
        {
            // When the document changes (collection is reset), scroll to top and restart timer
            if (e.Action == NotifyCollectionChangedAction.Reset)
            {
                PdfScroller.ScrollToTop();
                _scrollTimer.Start();
            }
        }

        protected override void OnClosing(CancelEventArgs e)
        {
            _scrollTimer.Stop();
            _viewModel.Dispose();
            base.OnClosing(e);
        }
    }
}
<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net9.0-windows</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <UseWPF>true</UseWPF>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="PdfiumViewer" Version="2.13.0" />
    <PackageReference Include="System.Drawing.Common" Version="8.0.6" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="PDFium.Windows" Version="1.0.0" />
  </ItemGroup>

  <ItemGroup>
    <None Update="config.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
    <None Update="x64/pdfium.dll">
   <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
 </None>
 <None Update="x86/pdfium.dll">
   <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
 </None>
  </ItemGroup>

</Project>
﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)pdfium.windows\1.0.0\build\PDFium.Windows.targets" Condition="Exists('$(NuGetPackageRoot)pdfium.windows\1.0.0\build\PDFium.Windows.targets')" />
  </ImportGroup>
</Project>
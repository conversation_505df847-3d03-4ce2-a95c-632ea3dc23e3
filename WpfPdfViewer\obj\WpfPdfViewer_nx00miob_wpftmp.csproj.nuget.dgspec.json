{"format": 1, "restore": {"D:\\jscode\\online_pdf\\WpfPdfViewer\\WpfPdfViewer.csproj": {}}, "projects": {"D:\\jscode\\online_pdf\\WpfPdfViewer\\WpfPdfViewer.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\jscode\\online_pdf\\WpfPdfViewer\\WpfPdfViewer.csproj", "projectName": "WpfPdfViewer", "projectPath": "D:\\jscode\\online_pdf\\WpfPdfViewer\\WpfPdfViewer.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\jscode\\online_pdf\\WpfPdfViewer\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "all"}, "SdkAnalysisLevel": "9.0.100"}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"PdfiumViewer.WPF": {"target": "Package", "version": "[1.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.100/PortableRuntimeIdentifierGraph.json"}}}}}
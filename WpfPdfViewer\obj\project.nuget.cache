{"version": 2, "dgSpecHash": "aPf3+KYiszQ=", "success": true, "projectFilePath": "D:\\jscode\\online_pdf\\WpfPdfViewer\\WpfPdfViewer.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\microsoft.win32.systemevents\\8.0.0\\microsoft.win32.systemevents.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\newtonsoft.json\\13.0.3\\newtonsoft.json.13.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\pdfium.windows\\1.0.0\\pdfium.windows.1.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\pdfiumviewer\\2.13.0\\pdfiumviewer.2.13.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.drawing.common\\8.0.6\\system.drawing.common.8.0.6.nupkg.sha512"], "logs": [{"code": "NU1701", "level": "Warning", "warningLevel": 1, "message": "已使用“.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1”而不是项目目标框架“net9.0-windows7.0”还原包“PdfiumViewer 2.13.0”。此包可能与项目不完全兼容。", "libraryId": "PdfiumViewer", "targetGraphs": ["net9.0-windows7.0"]}]}
^D:\JSCODE\ONLINE_PDF\FLUTTER_PDF_VIEWER\BUILD\WINDOWS\X64\PDFIUM-DOWNLOAD\CMAKEFILES\34CBB2E032D8296DBF53A724FC9E07E2\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\Microsoft Visual Studio\2022\Community\Common7\IDE\CommonExtensions\Microsoft\CMake\CMake\bin\cmake.exe" -SD:/jscode/online_pdf/flutter_pdf_viewer/build/windows/x64/pdfium-download -BD:/jscode/online_pdf/flutter_pdf_viewer/build/windows/x64/pdfium-download --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file D:/jscode/online_pdf/flutter_pdf_viewer/build/windows/x64/pdfium-download/pdfium-download.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd

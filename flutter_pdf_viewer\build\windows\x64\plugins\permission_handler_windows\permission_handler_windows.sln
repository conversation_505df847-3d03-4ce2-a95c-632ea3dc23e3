﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ALL_BUILD", "ALL_BUILD.vcxproj", "{E5DDC5D4-C333-3CF6-9AD8-0522203F360F}"
	ProjectSection(ProjectDependencies) = postProject
		{28BA3AFC-2BCA-329F-97A5-108CFAF6F45C} = {28BA3AFC-2BCA-329F-97A5-108CFAF6F45C}
		{CA997624-1288-3D09-B76C-F853B7E1B355} = {CA997624-1288-3D09-B76C-F853B7E1B355}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "INSTALL", "INSTALL.vcxproj", "{571BE5CF-3917-373E-B2EF-816EB16690F4}"
	ProjectSection(ProjectDependencies) = postProject
		{E5DDC5D4-C333-3CF6-9AD8-0522203F360F} = {E5DDC5D4-C333-3CF6-9AD8-0522203F360F}
		{28BA3AFC-2BCA-329F-97A5-108CFAF6F45C} = {28BA3AFC-2BCA-329F-97A5-108CFAF6F45C}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "ZERO_CHECK", "..\..\\ZERO_CHECK.vcxproj", "{28BA3AFC-2BCA-329F-97A5-108CFAF6F45C}"
	ProjectSection(ProjectDependencies) = postProject
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_assemble", "..\..\flutter\flutter_assemble.vcxproj", "{6FDF85FE-3C57-3EC1-83F0-76C2BE68E24F}"
	ProjectSection(ProjectDependencies) = postProject
		{28BA3AFC-2BCA-329F-97A5-108CFAF6F45C} = {28BA3AFC-2BCA-329F-97A5-108CFAF6F45C}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "flutter_wrapper_plugin", "..\..\flutter\flutter_wrapper_plugin.vcxproj", "{3EE9B191-2951-39C2-AB1B-618F951E8606}"
	ProjectSection(ProjectDependencies) = postProject
		{28BA3AFC-2BCA-329F-97A5-108CFAF6F45C} = {28BA3AFC-2BCA-329F-97A5-108CFAF6F45C}
		{6FDF85FE-3C57-3EC1-83F0-76C2BE68E24F} = {6FDF85FE-3C57-3EC1-83F0-76C2BE68E24F}
	EndProjectSection
EndProject
Project("{8BC9CEB8-8B4A-11D0-8D11-00A0C91BC942}") = "permission_handler_windows_plugin", "permission_handler_windows_plugin.vcxproj", "{CA997624-1288-3D09-B76C-F853B7E1B355}"
	ProjectSection(ProjectDependencies) = postProject
		{28BA3AFC-2BCA-329F-97A5-108CFAF6F45C} = {28BA3AFC-2BCA-329F-97A5-108CFAF6F45C}
		{6FDF85FE-3C57-3EC1-83F0-76C2BE68E24F} = {6FDF85FE-3C57-3EC1-83F0-76C2BE68E24F}
		{3EE9B191-2951-39C2-AB1B-618F951E8606} = {3EE9B191-2951-39C2-AB1B-618F951E8606}
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|x64 = Debug|x64
		Profile|x64 = Profile|x64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{E5DDC5D4-C333-3CF6-9AD8-0522203F360F}.Debug|x64.ActiveCfg = Debug|x64
		{E5DDC5D4-C333-3CF6-9AD8-0522203F360F}.Debug|x64.Build.0 = Debug|x64
		{E5DDC5D4-C333-3CF6-9AD8-0522203F360F}.Profile|x64.ActiveCfg = Profile|x64
		{E5DDC5D4-C333-3CF6-9AD8-0522203F360F}.Profile|x64.Build.0 = Profile|x64
		{E5DDC5D4-C333-3CF6-9AD8-0522203F360F}.Release|x64.ActiveCfg = Release|x64
		{E5DDC5D4-C333-3CF6-9AD8-0522203F360F}.Release|x64.Build.0 = Release|x64
		{571BE5CF-3917-373E-B2EF-816EB16690F4}.Debug|x64.ActiveCfg = Debug|x64
		{571BE5CF-3917-373E-B2EF-816EB16690F4}.Profile|x64.ActiveCfg = Profile|x64
		{571BE5CF-3917-373E-B2EF-816EB16690F4}.Release|x64.ActiveCfg = Release|x64
		{28BA3AFC-2BCA-329F-97A5-108CFAF6F45C}.Debug|x64.ActiveCfg = Debug|x64
		{28BA3AFC-2BCA-329F-97A5-108CFAF6F45C}.Debug|x64.Build.0 = Debug|x64
		{28BA3AFC-2BCA-329F-97A5-108CFAF6F45C}.Profile|x64.ActiveCfg = Profile|x64
		{28BA3AFC-2BCA-329F-97A5-108CFAF6F45C}.Profile|x64.Build.0 = Profile|x64
		{28BA3AFC-2BCA-329F-97A5-108CFAF6F45C}.Release|x64.ActiveCfg = Release|x64
		{28BA3AFC-2BCA-329F-97A5-108CFAF6F45C}.Release|x64.Build.0 = Release|x64
		{6FDF85FE-3C57-3EC1-83F0-76C2BE68E24F}.Debug|x64.ActiveCfg = Debug|x64
		{6FDF85FE-3C57-3EC1-83F0-76C2BE68E24F}.Debug|x64.Build.0 = Debug|x64
		{6FDF85FE-3C57-3EC1-83F0-76C2BE68E24F}.Profile|x64.ActiveCfg = Profile|x64
		{6FDF85FE-3C57-3EC1-83F0-76C2BE68E24F}.Profile|x64.Build.0 = Profile|x64
		{6FDF85FE-3C57-3EC1-83F0-76C2BE68E24F}.Release|x64.ActiveCfg = Release|x64
		{6FDF85FE-3C57-3EC1-83F0-76C2BE68E24F}.Release|x64.Build.0 = Release|x64
		{3EE9B191-2951-39C2-AB1B-618F951E8606}.Debug|x64.ActiveCfg = Debug|x64
		{3EE9B191-2951-39C2-AB1B-618F951E8606}.Debug|x64.Build.0 = Debug|x64
		{3EE9B191-2951-39C2-AB1B-618F951E8606}.Profile|x64.ActiveCfg = Profile|x64
		{3EE9B191-2951-39C2-AB1B-618F951E8606}.Profile|x64.Build.0 = Profile|x64
		{3EE9B191-2951-39C2-AB1B-618F951E8606}.Release|x64.ActiveCfg = Release|x64
		{3EE9B191-2951-39C2-AB1B-618F951E8606}.Release|x64.Build.0 = Release|x64
		{CA997624-1288-3D09-B76C-F853B7E1B355}.Debug|x64.ActiveCfg = Debug|x64
		{CA997624-1288-3D09-B76C-F853B7E1B355}.Debug|x64.Build.0 = Debug|x64
		{CA997624-1288-3D09-B76C-F853B7E1B355}.Profile|x64.ActiveCfg = Profile|x64
		{CA997624-1288-3D09-B76C-F853B7E1B355}.Profile|x64.Build.0 = Profile|x64
		{CA997624-1288-3D09-B76C-F853B7E1B355}.Release|x64.ActiveCfg = Release|x64
		{CA997624-1288-3D09-B76C-F853B7E1B355}.Release|x64.Build.0 = Release|x64
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {339A1739-F77C-3930-9C3F-3931FB24A68D}
	EndGlobalSection
	GlobalSection(ExtensibilityAddIns) = postSolution
	EndGlobalSection
EndGlobal

import 'dart:convert';
import 'dart:io';
import 'package:path/path.dart' as path;

class ConfigService {
  Future<Map<String, dynamic>> readConfig() async {
    try {
      // Get the directory of the executable
      String exePath = Platform.resolvedExecutable;
      String exeDir = path.dirname(exePath);
      
      // Construct the path to config.json
      String configPath = path.join(exeDir, 'config.json');
      
      File configFile = File(configPath);
      
      if (await configFile.exists()) {
        String content = await configFile.readAsString();
        return json.decode(content) as Map<String, dynamic>;
      } else {
        // Fallback to a default config if the file doesn't exist
        return {
          "pdfFolderPath": "pdfs",
          "secondsPerPage": 5
        };
      }
    } catch (e) {
      // Return default config in case of any error
      print('Error reading config file: $e');
      return {
        "pdfFolderPath": "pdfs",
        "secondsPerPage": 5
      };
    }
  }
}
import 'dart:io';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';

class PdfFileService {
  Future<List<File>> getPdfFiles(String directoryPath) async {
    if (Platform.isAndroid || Platform.isIOS) {
      var status = await Permission.storage.status;
      if (!status.isGranted) {
        await Permission.storage.request();
      }
    }

    final path = '$directoryPath';
    final directory = Directory(path);

    if (!await directory.exists()) {
      // Create the directory if it doesn't exist.
      await directory.create(recursive: true);
      // Maybe copy some sample PDFs from assets here for first run.
      print('Created directory: $path');
      return [];
    }

    final files = directory.listSync();
    return files
        .where((file) => file.path.toLowerCase().endsWith('.pdf'))
        .map((file) => File(file.path))
        .toList();
  }
}
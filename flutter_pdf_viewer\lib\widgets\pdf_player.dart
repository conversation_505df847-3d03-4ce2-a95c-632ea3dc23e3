import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:pdfx/pdfx.dart';
import 'package:flutter_pdf_viewer/widgets/pdf_view_wrapper.dart';

class PdfPlayer extends StatefulWidget {
  final List<File> pdfFiles;
  final Map<String, dynamic> config;

  const PdfPlayer({
    super.key,
    required this.pdfFiles,
    required this.config,
  });

  @override
  State<PdfPlayer> createState() => _PdfPlayerState();
}

class _PdfPlayerState extends State<PdfPlayer> {
  int _currentPdfIndex = 0;
  late PdfController _pdfController;
  Timer? _pageTurnTimer;
  Timer? _nextFileTimer;
  int _pageCount = 0;
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _initializePdfController();
    // Start the playback loop
    _playCurrentPdf();
  }

  @override
  void dispose() {
    _pageTurnTimer?.cancel();
    _nextFileTimer?.cancel();
    _pdfController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _initializePdfController() {
    final file = widget.pdfFiles[_currentPdfIndex];
    _pdfController = PdfController(
      document: PdfDocument.openFile(file.path),
    );
  }

  void _playCurrentPdf() {
    // Reset state for the new PDF
    _pageTurnTimer?.cancel();
    _nextFileTimer?.cancel();

    // Re-initialize the controller for the new file
    if (mounted) {
      setState(() {
        _initializePdfController();
      });
    }
    // The actual scrolling will start once the PDF is rendered,
    // triggered by the onRender callback in PdfViewWrapper.
  }

  void _startPageAnimation(int pageCount) {
    if (!mounted) return;
    _pageCount = pageCount;

    // 重置滚动位置到顶部
    _scrollController.jumpTo(0);

    // 开始自动滚动
    _startAutoScroll();
  }

  void _startAutoScroll() {
    if (!mounted) return;

    final int secondsPerPage = widget.config['secondsPerPage'] ?? 10;
    final int scrollDelay = widget.config['scrollDelay'] ?? 50;

    // 计算总的滚动时间（所有页面的总时间）
    final totalScrollTime = secondsPerPage * _pageCount * 1000; // 转换为毫秒
    final scrollSteps = totalScrollTime ~/ scrollDelay;

    if (scrollSteps <= 0) return;

    int currentStep = 0;

    _pageTurnTimer = Timer.periodic(Duration(milliseconds: scrollDelay), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }

      currentStep++;

      // 计算当前应该滚动到的位置
      final progress = currentStep / scrollSteps;
      final maxScrollExtent = _scrollController.position.maxScrollExtent;
      final targetPosition = maxScrollExtent * progress;

      if (targetPosition <= maxScrollExtent && currentStep < scrollSteps) {
        _scrollController.animateTo(
          targetPosition,
          duration: Duration(milliseconds: scrollDelay ~/ 2),
          curve: Curves.linear,
        );
      } else {
        // 滚动完成，切换到下一个文件
        timer.cancel();
        _scheduleNextFile();
      }
    });
  }

  void _scheduleNextFile() {
    final int delaySeconds = widget.config['pageChangeDelay'] ?? 5;
    _nextFileTimer = Timer(Duration(seconds: delaySeconds), () {
      if (!mounted) return;
      setState(() {
        _currentPdfIndex = (_currentPdfIndex + 1) % widget.pdfFiles.length;
        _playCurrentPdf();
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    if (widget.pdfFiles.isEmpty) {
      return const Center(child: Text('No PDF files found.'));
    }

    return SingleChildScrollView(
      controller: _scrollController,
      child: PdfViewWrapper(
        key: ValueKey(_currentPdfIndex), // Important to force widget recreation
        pdfController: _pdfController,
        scrollController: _scrollController,
        onRender: (pages) {
          // Delay to ensure layout is complete
          Future.delayed(const Duration(milliseconds: 500), () {
            _startPageAnimation(pages);
          });
        },
      ),
    );
  }
}
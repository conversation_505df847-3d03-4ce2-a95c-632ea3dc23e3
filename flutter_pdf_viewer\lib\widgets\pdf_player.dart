import 'dart:async';
import 'dart:io';
import 'package:flutter/material.dart';
import 'package:pdfx/pdfx.dart';
import 'package:flutter_pdf_viewer/widgets/pdf_view_wrapper.dart';

class PdfPlayer extends StatefulWidget {
  final List<File> pdfFiles;
  final Map<String, dynamic> config;

  const PdfPlayer({
    super.key,
    required this.pdfFiles,
    required this.config,
  });

  @override
  State<PdfPlayer> createState() => _PdfPlayerState();
}

class _PdfPlayerState extends State<PdfPlayer> {
  int _currentPdfIndex = 0;
  late PdfController _pdfController;
  Timer? _pageTurnTimer;
  Timer? _nextFileTimer;
  int _pageCount = 0;

  @override
  void initState() {
    super.initState();
    _initializePdfController();
    // Start the playback loop
    _playCurrentPdf();
  }

  @override
  void dispose() {
    _pageTurnTimer?.cancel();
    _nextFileTimer?.cancel();
    _pdfController.dispose();
    super.dispose();
  }

  void _initializePdfController() {
    final file = widget.pdfFiles[_currentPdfIndex];
    _pdfController = PdfController(
      document: PdfDocument.openFile(file.path),
    );
  }

  void _playCurrentPdf() {
    // Reset state for the new PDF
    _pageTurnTimer?.cancel();
    _nextFileTimer?.cancel();

    // Re-initialize the controller for the new file
    if (mounted) {
      setState(() {
        _initializePdfController();
      });
    }
    // The actual scrolling will start once the PDF is rendered,
    // triggered by the onRender callback in PdfViewWrapper.
  }

  void _startPageAnimation(int pageCount) {
    if (!mounted) return;
    _pageCount = pageCount;
    final int secondsPerPage = widget.config['secondsPerPage'] ?? 10;
    final duration = Duration(seconds: secondsPerPage);

    _pageTurnTimer = Timer.periodic(duration, (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }
      final currentPage = _pdfController.page;
      if (currentPage < _pageCount) {
        _pdfController.animateToPage(
          currentPage + 1,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      } else {
        timer.cancel();
        _scheduleNextFile();
      }
    });
  }

  void _scheduleNextFile() {
    final int delaySeconds = widget.config['pageChangeDelay'] ?? 5;
    _nextFileTimer = Timer(Duration(seconds: delaySeconds), () {
      if (!mounted) return;
      setState(() {
        _currentPdfIndex = (_currentPdfIndex + 1) % widget.pdfFiles.length;
        _playCurrentPdf();
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    if (widget.pdfFiles.isEmpty) {
      return const Center(child: Text('No PDF files found.'));
    }

    return PdfViewWrapper(
      key: ValueKey(_currentPdfIndex), // Important to force widget recreation
      pdfController: _pdfController,
      onRender: (pages) {
        // Delay to ensure layout is complete
        Future.delayed(const Duration(milliseconds: 500), () {
          _startPageAnimation(pages);
        });
      },
    );
  }
}
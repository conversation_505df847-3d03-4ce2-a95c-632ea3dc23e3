import 'dart:io';
import 'package:flutter/material.dart';
import 'package:pdfx/pdfx.dart';

class PdfViewWrapper extends StatefulWidget {
  final PdfController pdfController;
  final Function(int) onRender;
  final ScrollController? scrollController;

  const PdfViewWrapper({
    super.key,
    required this.pdfController,
    required this.onRender,
    this.scrollController,
  });

  @override
  State<PdfViewWrapper> createState() => _PdfViewWrapperState();
}

class _PdfViewWrapperState extends State<PdfViewWrapper> {
  @override
  Widget build(BuildContext context) {
    return PdfView(
      builders: PdfViewBuilders<DefaultBuilderOptions>(
        options: const DefaultBuilderOptions(),
        documentLoaderBuilder: (_) =>
            const Center(child: CircularProgressIndicator()),
        pageLoaderBuilder: (_) =>
            const Center(child: CircularProgressIndicator()),
        pageBuilder: _pageBuilder,
      ),
      controller: widget.pdfController,
      scrollDirection: Axis.vertical,
      // scrollController: widget.scrollController, // PdfView doesn't support this parameter
      onDocumentLoaded: (document) {
        widget.onRender(document.pagesCount);
      },
    );
  }

  PhotoViewGalleryPageOptions _pageBuilder(
    BuildContext context,
    Future<PdfPageImage> pageImage,
    int index,
    PdfDocument document,
  ) {
    return PhotoViewGalleryPageOptions(
      imageProvider: PdfPageImageProvider(
        pageImage,
        index,
        document.id,
      ),
      minScale: PhotoViewComputedScale.contained * 1,
      maxScale: PhotoViewComputedScale.contained * 3.0,
      initialScale: PhotoViewComputedScale.contained * 1.0,
      heroAttributes: PhotoViewHeroAttributes(tag: '${document.id}-$index'),
    );
  }
}
# 离线JavaScript库

本文件夹包含应用所需的离线JavaScript库文件，避免依赖在线CDN。

## 包含的库文件

### PDF.js v2.10.377
- `pdf.min.js` - PDF.js 核心库
- `pdf.worker.min.js` - PDF.js Web Worker

## 更新库文件

如需更新PDF.js版本，请：

1. 访问 https://cdnjs.com/libraries/pdf.js
2. 选择所需版本
3. 下载以下文件：
   - `pdf.min.js`
   - `pdf.worker.min.js`
4. 替换本文件夹中的对应文件
5. 如果版本号变化，需要同时更新 `renderer.js` 中的worker路径

## 使用说明

- 应用启动时会自动加载这些离线库
- 无需网络连接即可正常使用PDF查看功能
- 库文件总大小约 1.5MB

/**
 * @licstart The following is the entire license notice for the
 * Javascript code in this page
 *
 * Copyright 2021 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * @licend The above is the entire license notice for the
 * Javascript code in this page
 */
!function webpackUniversalModuleDefinition(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("pdfjs-dist/build/pdf",[],t):"object"==typeof exports?exports["pdfjs-dist/build/pdf"]=t():e["pdfjs-dist/build/pdf"]=e.pdfjsLib=t()}(this,(function(){return(()=>{"use strict";var __webpack_modules__=[,(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});t.addLinkAttributes=function addLinkAttributes(e,{url:t,target:r,rel:n,enabled:o=!0}={}){(0,s.assert)(t&&"string"==typeof t,'addLinkAttributes: A valid "url" parameter must provided.');const l=(0,s.removeNullCharacters)(t);if(o)e.href=e.title=l;else{e.href="";e.title=`Disabled: ${l}`;e.onclick=()=>!1}let c="";switch(r){case i.NONE:break;case i.SELF:c="_self";break;case i.BLANK:c="_blank";break;case i.PARENT:c="_parent";break;case i.TOP:c="_top"}e.target=c;e.rel="string"==typeof n?n:a};t.deprecated=function deprecated(e){console.log("Deprecated API usage: "+e)};t.getFilenameFromUrl=function getFilenameFromUrl(e){const t=e.indexOf("#"),r=e.indexOf("?"),s=Math.min(t>0?t:e.length,r>0?r:e.length);return e.substring(e.lastIndexOf("/",s)+1,s)};t.getPdfFilenameFromUrl=function getPdfFilenameFromUrl(e,t="document.pdf"){if("string"!=typeof e)return t;if(isDataScheme(e)){(0,s.warn)('getPdfFilenameFromUrl: ignore "data:"-URL for performance reasons.');return t}const r=/[^/?#=]+\.pdf\b(?!.*\.pdf\b)/i,n=/^(?:(?:[^:]+:)?\/\/[^/]+)?([^?#]*)(\?[^#]*)?(#.*)?$/.exec(e);let a=r.exec(n[1])||r.exec(n[2])||r.exec(n[3]);if(a){a=a[0];if(a.includes("%"))try{a=r.exec(decodeURIComponent(a))[0]}catch(e){}}return a||t};t.getXfaPageViewport=function getXfaPageViewport(e,{scale:t=1,rotation:r=0}){const{width:s,height:n}=e.attributes.style,a=[0,0,parseInt(s),parseInt(n)];return new PageViewport({viewBox:a,scale:t,rotation:r})};t.isDataScheme=isDataScheme;t.isPdfFile=function isPdfFile(e){return"string"==typeof e&&/\.pdf$/i.test(e)};t.isValidFetchUrl=isValidFetchUrl;t.loadScript=function loadScript(e,t=!1){return new Promise(((r,s)=>{const n=document.createElement("script");n.src=e;n.onload=function(e){t&&n.remove();r(e)};n.onerror=function(){s(new Error(`Cannot load script at: ${n.src}`))};(document.head||document.documentElement).appendChild(n)}))};t.StatTimer=t.RenderingCancelledException=t.PDFDateString=t.PageViewport=t.LinkTarget=t.DOMSVGFactory=t.DOMStandardFontDataFactory=t.DOMCMapReaderFactory=t.DOMCanvasFactory=t.DEFAULT_LINK_REL=void 0;var s=r(2),n=r(5);const a="noopener noreferrer nofollow";t.DEFAULT_LINK_REL=a;class DOMCanvasFactory extends n.BaseCanvasFactory{constructor({ownerDocument:e=globalThis.document}={}){super();this._document=e}_createCanvas(e,t){const r=this._document.createElement("canvas");r.width=e;r.height=t;return r}}t.DOMCanvasFactory=DOMCanvasFactory;async function fetchData(e,t=!1){if(isValidFetchUrl(e,document.baseURI)){const r=await fetch(e);if(!r.ok)throw new Error(r.statusText);return t?new Uint8Array(await r.arrayBuffer()):(0,s.stringToBytes)(await r.text())}return new Promise(((r,n)=>{const a=new XMLHttpRequest;a.open("GET",e,!0);t&&(a.responseType="arraybuffer");a.onreadystatechange=()=>{if(a.readyState===XMLHttpRequest.DONE){if(200===a.status||0===a.status){let e;t&&a.response?e=new Uint8Array(a.response):!t&&a.responseText&&(e=(0,s.stringToBytes)(a.responseText));if(e){r(e);return}}n(new Error(a.statusText))}};a.send(null)}))}class DOMCMapReaderFactory extends n.BaseCMapReaderFactory{_fetchData(e,t){return fetchData(e,this.isCompressed).then((e=>({cMapData:e,compressionType:t})))}}t.DOMCMapReaderFactory=DOMCMapReaderFactory;class DOMStandardFontDataFactory extends n.BaseStandardFontDataFactory{_fetchData(e){return fetchData(e,!0)}}t.DOMStandardFontDataFactory=DOMStandardFontDataFactory;class DOMSVGFactory extends n.BaseSVGFactory{_createSVG(e){return document.createElementNS("http://www.w3.org/2000/svg",e)}}t.DOMSVGFactory=DOMSVGFactory;class PageViewport{constructor({viewBox:e,scale:t,rotation:r,offsetX:s=0,offsetY:n=0,dontFlip:a=!1}){this.viewBox=e;this.scale=t;this.rotation=r;this.offsetX=s;this.offsetY=n;const i=(e[2]+e[0])/2,o=(e[3]+e[1])/2;let l,c,h,d,u,p,g,f;(r%=360)<0&&(r+=360);switch(r){case 180:l=-1;c=0;h=0;d=1;break;case 90:l=0;c=1;h=1;d=0;break;case 270:l=0;c=-1;h=-1;d=0;break;case 0:l=1;c=0;h=0;d=-1;break;default:throw new Error("PageViewport: Invalid rotation, must be a multiple of 90 degrees.")}if(a){h=-h;d=-d}if(0===l){u=Math.abs(o-e[1])*t+s;p=Math.abs(i-e[0])*t+n;g=Math.abs(e[3]-e[1])*t;f=Math.abs(e[2]-e[0])*t}else{u=Math.abs(i-e[0])*t+s;p=Math.abs(o-e[1])*t+n;g=Math.abs(e[2]-e[0])*t;f=Math.abs(e[3]-e[1])*t}this.transform=[l*t,c*t,h*t,d*t,u-l*t*i-h*t*o,p-c*t*i-d*t*o];this.width=g;this.height=f}clone({scale:e=this.scale,rotation:t=this.rotation,offsetX:r=this.offsetX,offsetY:s=this.offsetY,dontFlip:n=!1}={}){return new PageViewport({viewBox:this.viewBox.slice(),scale:e,rotation:t,offsetX:r,offsetY:s,dontFlip:n})}convertToViewportPoint(e,t){return s.Util.applyTransform([e,t],this.transform)}convertToViewportRectangle(e){const t=s.Util.applyTransform([e[0],e[1]],this.transform),r=s.Util.applyTransform([e[2],e[3]],this.transform);return[t[0],t[1],r[0],r[1]]}convertToPdfPoint(e,t){return s.Util.applyInverseTransform([e,t],this.transform)}}t.PageViewport=PageViewport;class RenderingCancelledException extends s.BaseException{constructor(e,t){super(e);this.type=t}}t.RenderingCancelledException=RenderingCancelledException;const i={NONE:0,SELF:1,BLANK:2,PARENT:3,TOP:4};t.LinkTarget=i;function isDataScheme(e){const t=e.length;let r=0;for(;r<t&&""===e[r].trim();)r++;return"data:"===e.substring(r,r+5).toLowerCase()}t.StatTimer=class StatTimer{constructor(){this.started=Object.create(null);this.times=[]}time(e){e in this.started&&(0,s.warn)(`Timer is already running for ${e}`);this.started[e]=Date.now()}timeEnd(e){e in this.started||(0,s.warn)(`Timer has not been started for ${e}`);this.times.push({name:e,start:this.started[e],end:Date.now()});delete this.started[e]}toString(){const e=[];let t=0;for(const e of this.times){const r=e.name;r.length>t&&(t=r.length)}for(const r of this.times){const s=r.end-r.start;e.push(`${r.name.padEnd(t)} ${s}ms\n`)}return e.join("")}};function isValidFetchUrl(e,t){try{const{protocol:r}=t?new URL(e,t):new URL(e);return"http:"===r||"https:"===r}catch(e){return!1}}let o;t.PDFDateString=class PDFDateString{static toDateObject(e){if(!e||!(0,s.isString)(e))return null;o||(o=new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?"));const t=o.exec(e);if(!t)return null;const r=parseInt(t[1],10);let n=parseInt(t[2],10);n=n>=1&&n<=12?n-1:0;let a=parseInt(t[3],10);a=a>=1&&a<=31?a:1;let i=parseInt(t[4],10);i=i>=0&&i<=23?i:0;let l=parseInt(t[5],10);l=l>=0&&l<=59?l:0;let c=parseInt(t[6],10);c=c>=0&&c<=59?c:0;const h=t[7]||"Z";let d=parseInt(t[8],10);d=d>=0&&d<=23?d:0;let u=parseInt(t[9],10)||0;u=u>=0&&u<=59?u:0;if("-"===h){i+=d;l+=u}else if("+"===h){i-=d;l-=u}return new Date(Date.UTC(r,n,a,i,l,c))}}},(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});t.arrayByteLength=arrayByteLength;t.arraysToBytes=function arraysToBytes(e){const t=e.length;if(1===t&&e[0]instanceof Uint8Array)return e[0];let r=0;for(let s=0;s<t;s++)r+=arrayByteLength(e[s]);let s=0;const n=new Uint8Array(r);for(let r=0;r<t;r++){let t=e[r];t instanceof Uint8Array||(t="string"==typeof t?stringToBytes(t):new Uint8Array(t));const a=t.byteLength;n.set(t,s);s+=a}return n};t.assert=assert;t.bytesToString=function bytesToString(e){assert(null!==e&&"object"==typeof e&&void 0!==e.length,"Invalid argument for bytesToString");const t=e.length,r=8192;if(t<r)return String.fromCharCode.apply(null,e);const s=[];for(let n=0;n<t;n+=r){const a=Math.min(n+r,t),i=e.subarray(n,a);s.push(String.fromCharCode.apply(null,i))}return s.join("")};t.createObjectURL=function createObjectURL(e,t="",r=!1){if(URL.createObjectURL&&!r)return URL.createObjectURL(new Blob([e],{type:t}));const s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";let n=`data:${t};base64,`;for(let t=0,r=e.length;t<r;t+=3){const a=255&e[t],i=255&e[t+1],o=255&e[t+2];n+=s[a>>2]+s[(3&a)<<4|i>>4]+s[t+1<r?(15&i)<<2|o>>6:64]+s[t+2<r?63&o:64]}return n};t.createPromiseCapability=function createPromiseCapability(){const e=Object.create(null);let t=!1;Object.defineProperty(e,"settled",{get:()=>t});e.promise=new Promise((function(r,s){e.resolve=function(e){t=!0;r(e)};e.reject=function(e){t=!0;s(e)}}));return e};t.createValidAbsoluteUrl=function createValidAbsoluteUrl(e,t){if(!e)return null;try{const r=t?new URL(e,t):new URL(e);if(function _isValidProtocol(e){if(!e)return!1;switch(e.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}(r))return r}catch(e){}return null};t.escapeString=function escapeString(e){return e.replace(/([()\\\n\r])/g,(e=>"\n"===e?"\\n":"\r"===e?"\\r":`\\${e}`))};t.getModificationDate=function getModificationDate(e=new Date){return[e.getUTCFullYear().toString(),(e.getUTCMonth()+1).toString().padStart(2,"0"),e.getUTCDate().toString().padStart(2,"0"),e.getUTCHours().toString().padStart(2,"0"),e.getUTCMinutes().toString().padStart(2,"0"),e.getUTCSeconds().toString().padStart(2,"0")].join("")};t.getVerbosityLevel=function getVerbosityLevel(){return n};t.info=function info(e){n>=s.INFOS&&console.log(`Info: ${e}`)};t.isArrayBuffer=function isArrayBuffer(e){return"object"==typeof e&&null!==e&&void 0!==e.byteLength};t.isArrayEqual=function isArrayEqual(e,t){if(e.length!==t.length)return!1;for(let r=0,s=e.length;r<s;r++)if(e[r]!==t[r])return!1;return!0};t.isAscii=function isAscii(e){return/^[\x00-\x7F]*$/.test(e)};t.isBool=function isBool(e){return"boolean"==typeof e};t.isNum=function isNum(e){return"number"==typeof e};t.isSameOrigin=function isSameOrigin(e,t){let r;try{r=new URL(e);if(!r.origin||"null"===r.origin)return!1}catch(e){return!1}const s=new URL(t,r);return r.origin===s.origin};t.isString=function isString(e){return"string"==typeof e};t.objectFromMap=function objectFromMap(e){const t=Object.create(null);for(const[r,s]of e)t[r]=s;return t};t.objectSize=function objectSize(e){return Object.keys(e).length};t.removeNullCharacters=function removeNullCharacters(e){if("string"!=typeof e){warn("The argument for removeNullCharacters must be a string.");return e}return e.replace(i,"")};t.setVerbosityLevel=function setVerbosityLevel(e){Number.isInteger(e)&&(n=e)};t.shadow=shadow;t.string32=function string32(e){return String.fromCharCode(e>>24&255,e>>16&255,e>>8&255,255&e)};t.stringToBytes=stringToBytes;t.stringToPDFString=function stringToPDFString(e){const t=e.length,r=[];if("þ"===e[0]&&"ÿ"===e[1])for(let s=2;s<t;s+=2)r.push(String.fromCharCode(e.charCodeAt(s)<<8|e.charCodeAt(s+1)));else if("ÿ"===e[0]&&"þ"===e[1])for(let s=2;s<t;s+=2)r.push(String.fromCharCode(e.charCodeAt(s+1)<<8|e.charCodeAt(s)));else for(let s=0;s<t;++s){const t=h[e.charCodeAt(s)];r.push(t?String.fromCharCode(t):e.charAt(s))}return r.join("")};t.stringToUTF16BEString=function stringToUTF16BEString(e){const t=["þÿ"];for(let r=0,s=e.length;r<s;r++){const s=e.charCodeAt(r);t.push(String.fromCharCode(s>>8&255),String.fromCharCode(255&s))}return t.join("")};t.stringToUTF8String=function stringToUTF8String(e){return decodeURIComponent(escape(e))};t.unreachable=unreachable;t.utf8StringToString=function utf8StringToString(e){return unescape(encodeURIComponent(e))};t.warn=warn;t.VerbosityLevel=t.Util=t.UNSUPPORTED_FEATURES=t.UnknownErrorException=t.UnexpectedResponseException=t.TextRenderingMode=t.StreamType=t.PermissionFlag=t.PasswordResponses=t.PasswordException=t.PageActionEventType=t.OPS=t.MissingPDFException=t.IsLittleEndianCached=t.IsEvalSupportedCached=t.InvalidPDFException=t.ImageKind=t.IDENTITY_MATRIX=t.FormatError=t.FontType=t.FONT_IDENTITY_MATRIX=t.DocumentActionEventType=t.CMapCompressionType=t.BaseException=t.AnnotationType=t.AnnotationStateModelType=t.AnnotationReviewState=t.AnnotationReplyType=t.AnnotationMarkedState=t.AnnotationFlag=t.AnnotationFieldFlag=t.AnnotationBorderStyleType=t.AnnotationActionEventType=t.AbortException=void 0;r(3);t.IDENTITY_MATRIX=[1,0,0,1,0,0];t.FONT_IDENTITY_MATRIX=[.001,0,0,.001,0,0];t.PermissionFlag={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048};t.TextRenderingMode={FILL:0,STROKE:1,FILL_STROKE:2,INVISIBLE:3,FILL_ADD_TO_PATH:4,STROKE_ADD_TO_PATH:5,FILL_STROKE_ADD_TO_PATH:6,ADD_TO_PATH:7,FILL_STROKE_MASK:3,ADD_TO_PATH_FLAG:4};t.ImageKind={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3};t.AnnotationType={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26};t.AnnotationStateModelType={MARKED:"Marked",REVIEW:"Review"};t.AnnotationMarkedState={MARKED:"Marked",UNMARKED:"Unmarked"};t.AnnotationReviewState={ACCEPTED:"Accepted",REJECTED:"Rejected",CANCELLED:"Cancelled",COMPLETED:"Completed",NONE:"None"};t.AnnotationReplyType={GROUP:"Group",REPLY:"R"};t.AnnotationFlag={INVISIBLE:1,HIDDEN:2,PRINT:4,NOZOOM:8,NOROTATE:16,NOVIEW:32,READONLY:64,LOCKED:128,TOGGLENOVIEW:256,LOCKEDCONTENTS:512};t.AnnotationFieldFlag={READONLY:1,REQUIRED:2,NOEXPORT:4,MULTILINE:4096,PASSWORD:8192,NOTOGGLETOOFF:16384,RADIO:32768,PUSHBUTTON:65536,COMBO:131072,EDIT:262144,SORT:524288,FILESELECT:1048576,MULTISELECT:2097152,DONOTSPELLCHECK:4194304,DONOTSCROLL:8388608,COMB:16777216,RICHTEXT:33554432,RADIOSINUNISON:33554432,COMMITONSELCHANGE:67108864};t.AnnotationBorderStyleType={SOLID:1,DASHED:2,BEVELED:3,INSET:4,UNDERLINE:5};t.AnnotationActionEventType={E:"Mouse Enter",X:"Mouse Exit",D:"Mouse Down",U:"Mouse Up",Fo:"Focus",Bl:"Blur",PO:"PageOpen",PC:"PageClose",PV:"PageVisible",PI:"PageInvisible",K:"Keystroke",F:"Format",V:"Validate",C:"Calculate"};t.DocumentActionEventType={WC:"WillClose",WS:"WillSave",DS:"DidSave",WP:"WillPrint",DP:"DidPrint"};t.PageActionEventType={O:"PageOpen",C:"PageClose"};t.StreamType={UNKNOWN:"UNKNOWN",FLATE:"FLATE",LZW:"LZW",DCT:"DCT",JPX:"JPX",JBIG:"JBIG",A85:"A85",AHX:"AHX",CCF:"CCF",RLX:"RLX"};t.FontType={UNKNOWN:"UNKNOWN",TYPE1:"TYPE1",TYPE1STANDARD:"TYPE1STANDARD",TYPE1C:"TYPE1C",CIDFONTTYPE0:"CIDFONTTYPE0",CIDFONTTYPE0C:"CIDFONTTYPE0C",TRUETYPE:"TRUETYPE",CIDFONTTYPE2:"CIDFONTTYPE2",TYPE3:"TYPE3",OPENTYPE:"OPENTYPE",TYPE0:"TYPE0",MMTYPE1:"MMTYPE1"};const s={ERRORS:0,WARNINGS:1,INFOS:5};t.VerbosityLevel=s;t.CMapCompressionType={NONE:0,BINARY:1,STREAM:2};t.OPS={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotations:78,endAnnotations:79,beginAnnotation:80,endAnnotation:81,paintJpegXObject:82,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91};t.UNSUPPORTED_FEATURES={unknown:"unknown",forms:"forms",javaScript:"javaScript",signatures:"signatures",smask:"smask",shadingPattern:"shadingPattern",font:"font",errorTilingPattern:"errorTilingPattern",errorExtGState:"errorExtGState",errorXObject:"errorXObject",errorFontLoadType3:"errorFontLoadType3",errorFontState:"errorFontState",errorFontMissing:"errorFontMissing",errorFontTranslate:"errorFontTranslate",errorColorSpace:"errorColorSpace",errorOperatorList:"errorOperatorList",errorFontToUnicode:"errorFontToUnicode",errorFontLoadNative:"errorFontLoadNative",errorFontBuildPath:"errorFontBuildPath",errorFontGetPath:"errorFontGetPath",errorMarkedContent:"errorMarkedContent"};t.PasswordResponses={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};let n=s.WARNINGS;function warn(e){n>=s.WARNINGS&&console.log(`Warning: ${e}`)}function unreachable(e){throw new Error(e)}function assert(e,t){e||unreachable(t)}function shadow(e,t,r){Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!1});return r}const a=function BaseExceptionClosure(){function BaseException(e){this.constructor===BaseException&&unreachable("Cannot initialize BaseException.");this.message=e;this.name=this.constructor.name}BaseException.prototype=new Error;BaseException.constructor=BaseException;return BaseException}();t.BaseException=a;t.PasswordException=class PasswordException extends a{constructor(e,t){super(e);this.code=t}};t.UnknownErrorException=class UnknownErrorException extends a{constructor(e,t){super(e);this.details=t}};t.InvalidPDFException=class InvalidPDFException extends a{};t.MissingPDFException=class MissingPDFException extends a{};t.UnexpectedResponseException=class UnexpectedResponseException extends a{constructor(e,t){super(e);this.status=t}};t.FormatError=class FormatError extends a{};t.AbortException=class AbortException extends a{};const i=/\x00/g;function stringToBytes(e){assert("string"==typeof e,"Invalid argument for stringToBytes");const t=e.length,r=new Uint8Array(t);for(let s=0;s<t;++s)r[s]=255&e.charCodeAt(s);return r}function arrayByteLength(e){if(void 0!==e.length)return e.length;assert(void 0!==e.byteLength,"arrayByteLength - invalid argument.");return e.byteLength}const o={get value(){return shadow(this,"value",function isLittleEndian(){const e=new Uint8Array(4);e[0]=1;return 1===new Uint32Array(e.buffer,0,1)[0]}())}};t.IsLittleEndianCached=o;const l={get value(){return shadow(this,"value",function isEvalSupported(){try{new Function("");return!0}catch(e){return!1}}())}};t.IsEvalSupportedCached=l;const c=[...Array(256).keys()].map((e=>e.toString(16).padStart(2,"0")));class Util{static makeHexColor(e,t,r){return`#${c[e]}${c[t]}${c[r]}`}static transform(e,t){return[e[0]*t[0]+e[2]*t[1],e[1]*t[0]+e[3]*t[1],e[0]*t[2]+e[2]*t[3],e[1]*t[2]+e[3]*t[3],e[0]*t[4]+e[2]*t[5]+e[4],e[1]*t[4]+e[3]*t[5]+e[5]]}static applyTransform(e,t){return[e[0]*t[0]+e[1]*t[2]+t[4],e[0]*t[1]+e[1]*t[3]+t[5]]}static applyInverseTransform(e,t){const r=t[0]*t[3]-t[1]*t[2];return[(e[0]*t[3]-e[1]*t[2]+t[2]*t[5]-t[4]*t[3])/r,(-e[0]*t[1]+e[1]*t[0]+t[4]*t[1]-t[5]*t[0])/r]}static getAxialAlignedBoundingBox(e,t){const r=Util.applyTransform(e,t),s=Util.applyTransform(e.slice(2,4),t),n=Util.applyTransform([e[0],e[3]],t),a=Util.applyTransform([e[2],e[1]],t);return[Math.min(r[0],s[0],n[0],a[0]),Math.min(r[1],s[1],n[1],a[1]),Math.max(r[0],s[0],n[0],a[0]),Math.max(r[1],s[1],n[1],a[1])]}static inverseTransform(e){const t=e[0]*e[3]-e[1]*e[2];return[e[3]/t,-e[1]/t,-e[2]/t,e[0]/t,(e[2]*e[5]-e[4]*e[3])/t,(e[4]*e[1]-e[5]*e[0])/t]}static apply3dTransform(e,t){return[e[0]*t[0]+e[1]*t[1]+e[2]*t[2],e[3]*t[0]+e[4]*t[1]+e[5]*t[2],e[6]*t[0]+e[7]*t[1]+e[8]*t[2]]}static singularValueDecompose2dScale(e){const t=[e[0],e[2],e[1],e[3]],r=e[0]*t[0]+e[1]*t[2],s=e[0]*t[1]+e[1]*t[3],n=e[2]*t[0]+e[3]*t[2],a=e[2]*t[1]+e[3]*t[3],i=(r+a)/2,o=Math.sqrt((r+a)**2-4*(r*a-n*s))/2,l=i+o||1,c=i-o||1;return[Math.sqrt(l),Math.sqrt(c)]}static normalizeRect(e){const t=e.slice(0);if(e[0]>e[2]){t[0]=e[2];t[2]=e[0]}if(e[1]>e[3]){t[1]=e[3];t[3]=e[1]}return t}static intersect(e,t){function compare(e,t){return e-t}const r=[e[0],e[2],t[0],t[2]].sort(compare),s=[e[1],e[3],t[1],t[3]].sort(compare),n=[];e=Util.normalizeRect(e);t=Util.normalizeRect(t);if(!(r[0]===e[0]&&r[1]===t[0]||r[0]===t[0]&&r[1]===e[0]))return null;n[0]=r[1];n[2]=r[2];if(!(s[0]===e[1]&&s[1]===t[1]||s[0]===t[1]&&s[1]===e[1]))return null;n[1]=s[1];n[3]=s[2];return n}}t.Util=Util;const h=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,728,711,710,729,733,731,730,732,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,8226,8224,8225,8230,8212,8211,402,8260,8249,8250,8722,8240,8222,8220,8221,8216,8217,8218,8482,64257,64258,321,338,352,376,381,305,322,339,353,382,0,8364]},(e,t,r)=>{r(4)},(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});t.isNodeJS=void 0;const r=!("object"!=typeof process||process+""!="[object process]"||process.versions.nw||process.versions.electron&&process.type&&"browser"!==process.type);t.isNodeJS=r},(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});t.BaseSVGFactory=t.BaseStandardFontDataFactory=t.BaseCMapReaderFactory=t.BaseCanvasFactory=void 0;var s=r(2);class BaseCanvasFactory{constructor(){this.constructor===BaseCanvasFactory&&(0,s.unreachable)("Cannot initialize BaseCanvasFactory.")}create(e,t){if(e<=0||t<=0)throw new Error("Invalid canvas size");const r=this._createCanvas(e,t);return{canvas:r,context:r.getContext("2d")}}reset(e,t,r){if(!e.canvas)throw new Error("Canvas is not specified");if(t<=0||r<=0)throw new Error("Invalid canvas size");e.canvas.width=t;e.canvas.height=r}destroy(e){if(!e.canvas)throw new Error("Canvas is not specified");e.canvas.width=0;e.canvas.height=0;e.canvas=null;e.context=null}_createCanvas(e,t){(0,s.unreachable)("Abstract method `_createCanvas` called.")}}t.BaseCanvasFactory=BaseCanvasFactory;class BaseCMapReaderFactory{constructor({baseUrl:e=null,isCompressed:t=!1}){this.constructor===BaseCMapReaderFactory&&(0,s.unreachable)("Cannot initialize BaseCMapReaderFactory.");this.baseUrl=e;this.isCompressed=t}async fetch({name:e}){if(!this.baseUrl)throw new Error('The CMap "baseUrl" parameter must be specified, ensure that the "cMapUrl" and "cMapPacked" API parameters are provided.');if(!e)throw new Error("CMap name must be specified.");const t=this.baseUrl+e+(this.isCompressed?".bcmap":""),r=this.isCompressed?s.CMapCompressionType.BINARY:s.CMapCompressionType.NONE;return this._fetchData(t,r).catch((e=>{throw new Error(`Unable to load ${this.isCompressed?"binary ":""}CMap at: ${t}`)}))}_fetchData(e,t){(0,s.unreachable)("Abstract method `_fetchData` called.")}}t.BaseCMapReaderFactory=BaseCMapReaderFactory;class BaseStandardFontDataFactory{constructor({baseUrl:e=null}){this.constructor===BaseStandardFontDataFactory&&(0,s.unreachable)("Cannot initialize BaseStandardFontDataFactory.");this.baseUrl=e}async fetch({filename:e}){if(!this.baseUrl)throw new Error('The standard font "baseUrl" parameter must be specified, ensure that the "standardFontDataUrl" API parameter is provided.');if(!e)throw new Error("Font filename must be specified.");const t=`${this.baseUrl}${e}`;return this._fetchData(t).catch((e=>{throw new Error(`Unable to load font data at: ${t}`)}))}_fetchData(e){(0,s.unreachable)("Abstract method `_fetchData` called.")}}t.BaseStandardFontDataFactory=BaseStandardFontDataFactory;class BaseSVGFactory{constructor(){this.constructor===BaseSVGFactory&&(0,s.unreachable)("Cannot initialize BaseSVGFactory.")}create(e,t){if(e<=0||t<=0)throw new Error("Invalid SVG dimensions");const r=this._createSVG("svg:svg");r.setAttribute("version","1.1");r.setAttribute("width",`${e}px`);r.setAttribute("height",`${t}px`);r.setAttribute("preserveAspectRatio","none");r.setAttribute("viewBox",`0 0 ${e} ${t}`);return r}createElement(e){if("string"!=typeof e)throw new Error("Invalid SVG element type");return this._createSVG(e)}_createSVG(e){(0,s.unreachable)("Abstract method `_createSVG` called.")}}t.BaseSVGFactory=BaseSVGFactory},(__unused_webpack_module,exports,__w_pdfjs_require__)=>{Object.defineProperty(exports,"__esModule",{value:!0});exports.getDocument=getDocument;exports.setPDFNetworkStreamFactory=setPDFNetworkStreamFactory;exports.version=exports.PDFWorker=exports.PDFPageProxy=exports.PDFDocumentProxy=exports.PDFDataRangeTransport=exports.LoopbackPort=exports.DefaultStandardFontDataFactory=exports.DefaultCMapReaderFactory=exports.DefaultCanvasFactory=exports.build=void 0;var _util=__w_pdfjs_require__(2),_display_utils=__w_pdfjs_require__(1),_font_loader=__w_pdfjs_require__(7),_node_utils=__w_pdfjs_require__(8),_annotation_storage=__w_pdfjs_require__(9),_canvas=__w_pdfjs_require__(10),_worker_options=__w_pdfjs_require__(12),_is_node=__w_pdfjs_require__(4),_message_handler=__w_pdfjs_require__(13),_metadata=__w_pdfjs_require__(14),_optional_content_config=__w_pdfjs_require__(15),_transport_stream=__w_pdfjs_require__(16);const DEFAULT_RANGE_CHUNK_SIZE=65536,RENDERING_CANCELLED_TIMEOUT=100,DefaultCanvasFactory=_is_node.isNodeJS?_node_utils.NodeCanvasFactory:_display_utils.DOMCanvasFactory;exports.DefaultCanvasFactory=DefaultCanvasFactory;const DefaultCMapReaderFactory=_is_node.isNodeJS?_node_utils.NodeCMapReaderFactory:_display_utils.DOMCMapReaderFactory;exports.DefaultCMapReaderFactory=DefaultCMapReaderFactory;const DefaultStandardFontDataFactory=_is_node.isNodeJS?_node_utils.NodeStandardFontDataFactory:_display_utils.DOMStandardFontDataFactory;exports.DefaultStandardFontDataFactory=DefaultStandardFontDataFactory;let createPDFNetworkStream;function setPDFNetworkStreamFactory(e){createPDFNetworkStream=e}function getDocument(e){const t=new PDFDocumentLoadingTask;let r;if("string"==typeof e||e instanceof URL)r={url:e};else if((0,_util.isArrayBuffer)(e))r={data:e};else if(e instanceof PDFDataRangeTransport)r={range:e};else{if("object"!=typeof e)throw new Error("Invalid parameter in getDocument, need either string, URL, Uint8Array, or parameter object.");if(!e.url&&!e.data&&!e.range)throw new Error("Invalid parameter object: need either .data, .range or .url");r=e}const s=Object.create(null);let n=null,a=null;for(const e in r){const t=r[e];switch(e){case"url":if("undefined"!=typeof window)try{s[e]=new URL(t,window.location).href;continue}catch(e){(0,_util.warn)(`Cannot create valid URL: "${e}".`)}else if("string"==typeof t||t instanceof URL){s[e]=t.toString();continue}throw new Error("Invalid PDF url data: either string or URL-object is expected in the url property.");case"range":n=t;continue;case"worker":a=t;continue;case"data":if(_is_node.isNodeJS&&"undefined"!=typeof Buffer&&t instanceof Buffer)s[e]=new Uint8Array(t);else{if(t instanceof Uint8Array)break;if("string"==typeof t)s[e]=(0,_util.stringToBytes)(t);else if("object"!=typeof t||null===t||isNaN(t.length)){if(!(0,_util.isArrayBuffer)(t))throw new Error("Invalid PDF binary data: either typed array, string, or array-like object is expected in the data property.");s[e]=new Uint8Array(t)}else s[e]=new Uint8Array(t)}continue}s[e]=t}s.rangeChunkSize=s.rangeChunkSize||DEFAULT_RANGE_CHUNK_SIZE;s.CMapReaderFactory=s.CMapReaderFactory||DefaultCMapReaderFactory;s.StandardFontDataFactory=s.StandardFontDataFactory||DefaultStandardFontDataFactory;s.ignoreErrors=!0!==s.stopAtErrors;s.fontExtraProperties=!0===s.fontExtraProperties;s.pdfBug=!0===s.pdfBug;s.enableXfa=!0===s.enableXfa;("string"!=typeof s.docBaseUrl||(0,_display_utils.isDataScheme)(s.docBaseUrl))&&(s.docBaseUrl=null);Number.isInteger(s.maxImageSize)||(s.maxImageSize=-1);"boolean"!=typeof s.useWorkerFetch&&(s.useWorkerFetch=s.CMapReaderFactory===_display_utils.DOMCMapReaderFactory&&s.StandardFontDataFactory===_display_utils.DOMStandardFontDataFactory);"boolean"!=typeof s.isEvalSupported&&(s.isEvalSupported=!0);"boolean"!=typeof s.disableFontFace&&(s.disableFontFace=_is_node.isNodeJS);"boolean"!=typeof s.useSystemFonts&&(s.useSystemFonts=!_is_node.isNodeJS&&!s.disableFontFace);void 0===s.ownerDocument&&(s.ownerDocument=globalThis.document);"boolean"!=typeof s.disableRange&&(s.disableRange=!1);"boolean"!=typeof s.disableStream&&(s.disableStream=!1);"boolean"!=typeof s.disableAutoFetch&&(s.disableAutoFetch=!1);(0,_util.setVerbosityLevel)(s.verbosity);if(!a){const e={verbosity:s.verbosity,port:_worker_options.GlobalWorkerOptions.workerPort};a=e.port?PDFWorker.fromPort(e):new PDFWorker(e);t._worker=a}const i=t.docId;a.promise.then((function(){if(t.destroyed)throw new Error("Loading aborted");const e=_fetchDocument(a,s,n,i),r=new Promise((function(e){let t;n?t=new _transport_stream.PDFDataTransportStream({length:s.length,initialData:s.initialData,progressiveDone:s.progressiveDone,contentDispositionFilename:s.contentDispositionFilename,disableRange:s.disableRange,disableStream:s.disableStream},n):s.data||(t=createPDFNetworkStream({url:s.url,length:s.length,httpHeaders:s.httpHeaders,withCredentials:s.withCredentials,rangeChunkSize:s.rangeChunkSize,disableRange:s.disableRange,disableStream:s.disableStream}));e(t)}));return Promise.all([e,r]).then((function([e,r]){if(t.destroyed)throw new Error("Loading aborted");const n=new _message_handler.MessageHandler(i,e,a.port);n.postMessageTransfers=a.postMessageTransfers;const o=new WorkerTransport(n,t,r,s);t._transport=o;n.send("Ready",null)}))})).catch(t._capability.reject);return t}function _fetchDocument(e,t,r,s){if(e.destroyed)return Promise.reject(new Error("Worker was destroyed"));if(r){t.length=r.length;t.initialData=r.initialData;t.progressiveDone=r.progressiveDone;t.contentDispositionFilename=r.contentDispositionFilename}return e.messageHandler.sendWithPromise("GetDocRequest",{docId:s,apiVersion:"2.10.377",source:{data:t.data,url:t.url,password:t.password,disableAutoFetch:t.disableAutoFetch,rangeChunkSize:t.rangeChunkSize,length:t.length},maxImageSize:t.maxImageSize,disableFontFace:t.disableFontFace,postMessageTransfers:e.postMessageTransfers,docBaseUrl:t.docBaseUrl,ignoreErrors:t.ignoreErrors,isEvalSupported:t.isEvalSupported,fontExtraProperties:t.fontExtraProperties,enableXfa:t.enableXfa,useSystemFonts:t.useSystemFonts,cMapUrl:t.useWorkerFetch?t.cMapUrl:null,standardFontDataUrl:t.useWorkerFetch?t.standardFontDataUrl:null}).then((function(t){if(e.destroyed)throw new Error("Worker was destroyed");return t}))}const PDFDocumentLoadingTask=function PDFDocumentLoadingTaskClosure(){let e=0;return class PDFDocumentLoadingTask{constructor(){this._capability=(0,_util.createPromiseCapability)();this._transport=null;this._worker=null;this.docId="d"+e++;this.destroyed=!1;this.onPassword=null;this.onProgress=null;this.onUnsupportedFeature=null}get promise(){return this._capability.promise}destroy(){this.destroyed=!0;return(this._transport?this._transport.destroy():Promise.resolve()).then((()=>{this._transport=null;if(this._worker){this._worker.destroy();this._worker=null}}))}}}();class PDFDataRangeTransport{constructor(e,t,r=!1,s=null){this.length=e;this.initialData=t;this.progressiveDone=r;this.contentDispositionFilename=s;this._rangeListeners=[];this._progressListeners=[];this._progressiveReadListeners=[];this._progressiveDoneListeners=[];this._readyCapability=(0,_util.createPromiseCapability)()}addRangeListener(e){this._rangeListeners.push(e)}addProgressListener(e){this._progressListeners.push(e)}addProgressiveReadListener(e){this._progressiveReadListeners.push(e)}addProgressiveDoneListener(e){this._progressiveDoneListeners.push(e)}onDataRange(e,t){for(const r of this._rangeListeners)r(e,t)}onDataProgress(e,t){this._readyCapability.promise.then((()=>{for(const r of this._progressListeners)r(e,t)}))}onDataProgressiveRead(e){this._readyCapability.promise.then((()=>{for(const t of this._progressiveReadListeners)t(e)}))}onDataProgressiveDone(){this._readyCapability.promise.then((()=>{for(const e of this._progressiveDoneListeners)e()}))}transportReady(){this._readyCapability.resolve()}requestDataRange(e,t){(0,_util.unreachable)("Abstract method PDFDataRangeTransport.requestDataRange")}abort(){}}exports.PDFDataRangeTransport=PDFDataRangeTransport;class PDFDocumentProxy{constructor(e,t){this._pdfInfo=e;this._transport=t;Object.defineProperty(this,"fingerprint",{get(){(0,_display_utils.deprecated)("`PDFDocumentProxy.fingerprint`, please use `PDFDocumentProxy.fingerprints` instead.");return this.fingerprints[0]}})}get annotationStorage(){return this._transport.annotationStorage}get numPages(){return this._pdfInfo.numPages}get fingerprints(){return this._pdfInfo.fingerprints}get isPureXfa(){return!!this._transport._htmlForXfa}get allXfaHtml(){return this._transport._htmlForXfa}getPage(e){return this._transport.getPage(e)}getPageIndex(e){return this._transport.getPageIndex(e)}getDestinations(){return this._transport.getDestinations()}getDestination(e){return this._transport.getDestination(e)}getPageLabels(){return this._transport.getPageLabels()}getPageLayout(){return this._transport.getPageLayout()}getPageMode(){return this._transport.getPageMode()}getViewerPreferences(){return this._transport.getViewerPreferences()}getOpenAction(){return this._transport.getOpenAction()}getAttachments(){return this._transport.getAttachments()}getJavaScript(){return this._transport.getJavaScript()}getJSActions(){return this._transport.getDocJSActions()}getOutline(){return this._transport.getOutline()}getOptionalContentConfig(){return this._transport.getOptionalContentConfig()}getPermissions(){return this._transport.getPermissions()}getMetadata(){return this._transport.getMetadata()}getMarkInfo(){return this._transport.getMarkInfo()}getData(){return this._transport.getData()}getDownloadInfo(){return this._transport.downloadInfoCapability.promise}getStats(){return this._transport.getStats()}cleanup(e=!1){return this._transport.startCleanup(e||this.isPureXfa)}destroy(){return this.loadingTask.destroy()}get loadingParams(){return this._transport.loadingParams}get loadingTask(){return this._transport.loadingTask}saveDocument(){this._transport.annotationStorage.size<=0&&(0,_display_utils.deprecated)("saveDocument called while `annotationStorage` is empty, please use the getData-method instead.");return this._transport.saveDocument()}getFieldObjects(){return this._transport.getFieldObjects()}hasJSActions(){return this._transport.hasJSActions()}getCalculationOrderIds(){return this._transport.getCalculationOrderIds()}}exports.PDFDocumentProxy=PDFDocumentProxy;class PDFPageProxy{constructor(e,t,r,s,n=!1){this._pageIndex=e;this._pageInfo=t;this._ownerDocument=s;this._transport=r;this._stats=n?new _display_utils.StatTimer:null;this._pdfBug=n;this.commonObjs=r.commonObjs;this.objs=new PDFObjects;this.cleanupAfterRender=!1;this.pendingCleanup=!1;this._intentStates=new Map;this.destroyed=!1}get pageNumber(){return this._pageIndex+1}get rotate(){return this._pageInfo.rotate}get ref(){return this._pageInfo.ref}get userUnit(){return this._pageInfo.userUnit}get view(){return this._pageInfo.view}getViewport({scale:e,rotation:t=this.rotate,offsetX:r=0,offsetY:s=0,dontFlip:n=!1}={}){return new _display_utils.PageViewport({viewBox:this.view,scale:e,rotation:t,offsetX:r,offsetY:s,dontFlip:n})}getAnnotations({intent:e=null}={}){const t="display"===e||"print"===e?e:null;if(!this._annotationsPromise||this._annotationsIntent!==t){this._annotationsPromise=this._transport.getAnnotations(this._pageIndex,t);this._annotationsIntent=t}return this._annotationsPromise}getJSActions(){return this._jsActionsPromise||(this._jsActionsPromise=this._transport.getPageJSActions(this._pageIndex))}async getXfa(){return this._transport._htmlForXfa?.children[this._pageIndex]||null}render({canvasContext:e,viewport:t,intent:r="display",renderInteractiveForms:s=!1,transform:n=null,imageLayer:a=null,canvasFactory:i=null,background:o=null,includeAnnotationStorage:l=!1,optionalContentConfigPromise:c=null}){var h;this._stats&&this._stats.time("Overall");const d="print"===r?"print":"display";this.pendingCleanup=!1;c||(c=this._transport.getOptionalContentConfig());let u=this._intentStates.get(d);if(!u){u=Object.create(null);this._intentStates.set(d,u)}if(u.streamReaderCancelTimeout){clearTimeout(u.streamReaderCancelTimeout);u.streamReaderCancelTimeout=null}const p=i||new DefaultCanvasFactory({ownerDocument:this._ownerDocument}),g=l?this._transport.annotationStorage.serializable:null;if(!u.displayReadyCapability){u.displayReadyCapability=(0,_util.createPromiseCapability)();u.operatorList={fnArray:[],argsArray:[],lastChunk:!1};this._stats&&this._stats.time("Page Request");this._pumpOperatorList({pageIndex:this._pageIndex,intent:d,renderInteractiveForms:!0===s,annotationStorage:g})}const complete=e=>{u.renderTasks.delete(f);(this.cleanupAfterRender||"print"===d)&&(this.pendingCleanup=!0);this._tryCleanup();if(e){f.capability.reject(e);this._abortOperatorList({intentState:u,reason:e})}else f.capability.resolve();if(this._stats){this._stats.timeEnd("Rendering");this._stats.timeEnd("Overall")}},f=new InternalRenderTask({callback:complete,params:{canvasContext:e,viewport:t,transform:n,imageLayer:a,background:o},objs:this.objs,commonObjs:this.commonObjs,operatorList:u.operatorList,pageIndex:this._pageIndex,canvasFactory:p,useRequestAnimationFrame:"print"!==d,pdfBug:this._pdfBug});((h=u).renderTasks||(h.renderTasks=new Set)).add(f);const m=f.task;Promise.all([u.displayReadyCapability.promise,c]).then((([e,t])=>{if(this.pendingCleanup)complete();else{this._stats&&this._stats.time("Rendering");f.initializeGraphics({transparency:e,optionalContentConfig:t});f.operatorListChanged()}})).catch(complete);return m}getOperatorList({intent:e="display"}={}){const t="oplist-"+("print"===e?"print":"display");let r,s=this._intentStates.get(t);if(!s){s=Object.create(null);this._intentStates.set(t,s)}if(!s.opListReadCapability){var n;r=Object.create(null);r.operatorListChanged=function operatorListChanged(){if(s.operatorList.lastChunk){s.opListReadCapability.resolve(s.operatorList);s.renderTasks.delete(r)}};s.opListReadCapability=(0,_util.createPromiseCapability)();((n=s).renderTasks||(n.renderTasks=new Set)).add(r);s.operatorList={fnArray:[],argsArray:[],lastChunk:!1};this._stats&&this._stats.time("Page Request");this._pumpOperatorList({pageIndex:this._pageIndex,intent:t})}return s.opListReadCapability.promise}streamTextContent({normalizeWhitespace:e=!1,disableCombineTextItems:t=!1,includeMarkedContent:r=!1}={}){return this._transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this._pageIndex,normalizeWhitespace:!0===e,combineTextItems:!0!==t,includeMarkedContent:!0===r},{highWaterMark:100,size:e=>e.items.length})}getTextContent(e={}){const t=this.streamTextContent(e);return new Promise((function(e,r){const s=t.getReader(),n={items:[],styles:Object.create(null)};!function pump(){s.read().then((function({value:t,done:r}){if(r)e(n);else{Object.assign(n.styles,t.styles);n.items.push(...t.items);pump()}}),r)}()}))}getStructTree(){return this._structTreePromise||(this._structTreePromise=this._transport.getStructTree(this._pageIndex))}_destroy(){this.destroyed=!0;this._transport.pageCache[this._pageIndex]=null;const e=[];for(const[t,r]of this._intentStates){this._abortOperatorList({intentState:r,reason:new Error("Page was destroyed."),force:!0});if(!t.startsWith("oplist-"))for(const t of r.renderTasks){e.push(t.completed);t.cancel()}}this.objs.clear();this._annotationsPromise=null;this._jsActionsPromise=null;this._structTreePromise=null;this.pendingCleanup=!1;return Promise.all(e)}cleanup(e=!1){this.pendingCleanup=!0;return this._tryCleanup(e)}_tryCleanup(e=!1){if(!this.pendingCleanup)return!1;for(const{renderTasks:e,operatorList:t}of this._intentStates.values())if(e.size>0||!t.lastChunk)return!1;this._intentStates.clear();this.objs.clear();this._annotationsPromise=null;this._jsActionsPromise=null;this._structTreePromise=null;e&&this._stats&&(this._stats=new _display_utils.StatTimer);this.pendingCleanup=!1;return!0}_startRenderPage(e,t){const r=this._intentStates.get(t);if(r){this._stats&&this._stats.timeEnd("Page Request");r.displayReadyCapability&&r.displayReadyCapability.resolve(e)}}_renderPageChunk(e,t){for(let r=0,s=e.length;r<s;r++){t.operatorList.fnArray.push(e.fnArray[r]);t.operatorList.argsArray.push(e.argsArray[r])}t.operatorList.lastChunk=e.lastChunk;for(const e of t.renderTasks)e.operatorListChanged();e.lastChunk&&this._tryCleanup()}_pumpOperatorList(e){(0,_util.assert)(e.intent,'PDFPageProxy._pumpOperatorList: Expected "intent" argument.');const t=this._transport.messageHandler.sendWithStream("GetOperatorList",e).getReader(),r=this._intentStates.get(e.intent);r.streamReader=t;const pump=()=>{t.read().then((({value:e,done:t})=>{if(t)r.streamReader=null;else if(!this._transport.destroyed){this._renderPageChunk(e,r);pump()}}),(e=>{r.streamReader=null;if(!this._transport.destroyed){if(r.operatorList){r.operatorList.lastChunk=!0;for(const e of r.renderTasks)e.operatorListChanged();this._tryCleanup()}if(r.displayReadyCapability)r.displayReadyCapability.reject(e);else{if(!r.opListReadCapability)throw e;r.opListReadCapability.reject(e)}}}))};pump()}_abortOperatorList({intentState:e,reason:t,force:r=!1}){(0,_util.assert)(t instanceof Error||"object"==typeof t&&null!==t,'PDFPageProxy._abortOperatorList: Expected "reason" argument.');if(e.streamReader){if(!r){if(e.renderTasks.size>0)return;if(t instanceof _display_utils.RenderingCancelledException){e.streamReaderCancelTimeout=setTimeout((()=>{this._abortOperatorList({intentState:e,reason:t,force:!0});e.streamReaderCancelTimeout=null}),RENDERING_CANCELLED_TIMEOUT);return}}e.streamReader.cancel(new _util.AbortException(t?.message));e.streamReader=null;if(!this._transport.destroyed){for(const[t,r]of this._intentStates)if(r===e){this._intentStates.delete(t);break}this.cleanup()}}}get stats(){return this._stats}}exports.PDFPageProxy=PDFPageProxy;class LoopbackPort{constructor(){this._listeners=[];this._deferred=Promise.resolve(void 0)}postMessage(e,t){const r=new WeakMap,s={data:function cloneValue(e){if("function"==typeof e||"symbol"==typeof e||e instanceof URL)throw new Error(`LoopbackPort.postMessage - cannot clone: ${e?.toString()}`);if("object"!=typeof e||null===e)return e;if(r.has(e))return r.get(e);let s,n;if((s=e.buffer)&&(0,_util.isArrayBuffer)(s)){n=t?.includes(s)?new e.constructor(s,e.byteOffset,e.byteLength):new e.constructor(e);r.set(e,n);return n}if(e instanceof Map){n=new Map;r.set(e,n);for(const[t,r]of e)n.set(t,cloneValue(r));return n}if(e instanceof Set){n=new Set;r.set(e,n);for(const t of e)n.add(cloneValue(t));return n}n=Array.isArray(e)?[]:Object.create(null);r.set(e,n);for(const t in e){let r,s=e;for(;!(r=Object.getOwnPropertyDescriptor(s,t));)s=Object.getPrototypeOf(s);void 0!==r.value&&(("function"!=typeof r.value||e.hasOwnProperty?.(t))&&(n[t]=cloneValue(r.value)))}return n}(e)};this._deferred.then((()=>{for(const e of this._listeners)e.call(this,s)}))}addEventListener(e,t){this._listeners.push(t)}removeEventListener(e,t){const r=this._listeners.indexOf(t);this._listeners.splice(r,1)}terminate(){this._listeners.length=0}}exports.LoopbackPort=LoopbackPort;const PDFWorker=function PDFWorkerClosure(){const pdfWorkerPorts=new WeakMap;let isWorkerDisabled=!1,fallbackWorkerSrc,nextFakeWorkerId=0,fakeWorkerCapability;if(_is_node.isNodeJS&&"function"==typeof require){isWorkerDisabled=!0;fallbackWorkerSrc="./pdf.worker.js"}else if("object"==typeof document&&"currentScript"in document){const e=document.currentScript?.src;e&&(fallbackWorkerSrc=e.replace(/(\.(?:min\.)?js)(\?.*)?$/i,".worker$1$2"))}function getWorkerSrc(){if(_worker_options.GlobalWorkerOptions.workerSrc)return _worker_options.GlobalWorkerOptions.workerSrc;if(void 0!==fallbackWorkerSrc){_is_node.isNodeJS||(0,_display_utils.deprecated)('No "GlobalWorkerOptions.workerSrc" specified.');return fallbackWorkerSrc}throw new Error('No "GlobalWorkerOptions.workerSrc" specified.')}function getMainThreadWorkerMessageHandler(){let e;try{e=globalThis.pdfjsWorker?.WorkerMessageHandler}catch(e){}return e||null}function setupFakeWorkerGlobal(){if(fakeWorkerCapability)return fakeWorkerCapability.promise;fakeWorkerCapability=(0,_util.createPromiseCapability)();const loader=async function(){const mainWorkerMessageHandler=getMainThreadWorkerMessageHandler();if(mainWorkerMessageHandler)return mainWorkerMessageHandler;if(_is_node.isNodeJS&&"function"==typeof require){const worker=eval("require")(getWorkerSrc());return worker.WorkerMessageHandler}await(0,_display_utils.loadScript)(getWorkerSrc());return window.pdfjsWorker.WorkerMessageHandler};loader().then(fakeWorkerCapability.resolve,fakeWorkerCapability.reject);return fakeWorkerCapability.promise}function createCDNWrapper(e){const t="importScripts('"+e+"');";return URL.createObjectURL(new Blob([t]))}class PDFWorker{constructor({name:e=null,port:t=null,verbosity:r=(0,_util.getVerbosityLevel)()}={}){if(t&&pdfWorkerPorts.has(t))throw new Error("Cannot use more than one PDFWorker per port");this.name=e;this.destroyed=!1;this.postMessageTransfers=!0;this.verbosity=r;this._readyCapability=(0,_util.createPromiseCapability)();this._port=null;this._webWorker=null;this._messageHandler=null;if(t){pdfWorkerPorts.set(t,this);this._initializeFromPort(t)}else this._initialize()}get promise(){return this._readyCapability.promise}get port(){return this._port}get messageHandler(){return this._messageHandler}_initializeFromPort(e){this._port=e;this._messageHandler=new _message_handler.MessageHandler("main","worker",e);this._messageHandler.on("ready",(function(){}));this._readyCapability.resolve()}_initialize(){if("undefined"!=typeof Worker&&!isWorkerDisabled&&!getMainThreadWorkerMessageHandler()){let e=getWorkerSrc();try{(0,_util.isSameOrigin)(window.location.href,e)||(e=createCDNWrapper(new URL(e,window.location).href));const t=new Worker(e),r=new _message_handler.MessageHandler("main","worker",t),terminateEarly=()=>{t.removeEventListener("error",onWorkerError);r.destroy();t.terminate();this.destroyed?this._readyCapability.reject(new Error("Worker was destroyed")):this._setupFakeWorker()},onWorkerError=()=>{this._webWorker||terminateEarly()};t.addEventListener("error",onWorkerError);r.on("test",(e=>{t.removeEventListener("error",onWorkerError);if(this.destroyed)terminateEarly();else if(e){this._messageHandler=r;this._port=t;this._webWorker=t;e.supportTransfers||(this.postMessageTransfers=!1);this._readyCapability.resolve();r.send("configure",{verbosity:this.verbosity})}else{this._setupFakeWorker();r.destroy();t.terminate()}}));r.on("ready",(e=>{t.removeEventListener("error",onWorkerError);if(this.destroyed)terminateEarly();else try{sendTest()}catch(e){this._setupFakeWorker()}}));const sendTest=()=>{const e=new Uint8Array([this.postMessageTransfers?255:0]);try{r.send("test",e,[e.buffer])}catch(t){(0,_util.warn)("Cannot use postMessage transfers.");e[0]=0;r.send("test",e)}};sendTest();return}catch(e){(0,_util.info)("The worker has been disabled.")}}this._setupFakeWorker()}_setupFakeWorker(){if(!isWorkerDisabled){(0,_util.warn)("Setting up fake worker.");isWorkerDisabled=!0}setupFakeWorkerGlobal().then((e=>{if(this.destroyed){this._readyCapability.reject(new Error("Worker was destroyed"));return}const t=new LoopbackPort;this._port=t;const r="fake"+nextFakeWorkerId++,s=new _message_handler.MessageHandler(r+"_worker",r,t);e.setup(s,t);const n=new _message_handler.MessageHandler(r,r+"_worker",t);this._messageHandler=n;this._readyCapability.resolve();n.send("configure",{verbosity:this.verbosity})})).catch((e=>{this._readyCapability.reject(new Error(`Setting up fake worker failed: "${e.message}".`))}))}destroy(){this.destroyed=!0;if(this._webWorker){this._webWorker.terminate();this._webWorker=null}pdfWorkerPorts.delete(this._port);this._port=null;if(this._messageHandler){this._messageHandler.destroy();this._messageHandler=null}}static fromPort(e){if(!e||!e.port)throw new Error("PDFWorker.fromPort - invalid method signature.");return pdfWorkerPorts.has(e.port)?pdfWorkerPorts.get(e.port):new PDFWorker(e)}static getWorkerSrc(){return getWorkerSrc()}}return PDFWorker}();exports.PDFWorker=PDFWorker;class WorkerTransport{constructor(e,t,r,s){this.messageHandler=e;this.loadingTask=t;this.commonObjs=new PDFObjects;this.fontLoader=new _font_loader.FontLoader({docId:t.docId,onUnsupportedFeature:this._onUnsupportedFeature.bind(this),ownerDocument:s.ownerDocument,styleElement:s.styleElement});this._params=s;if(!s.useWorkerFetch){this.CMapReaderFactory=new s.CMapReaderFactory({baseUrl:s.cMapUrl,isCompressed:s.cMapPacked});this.StandardFontDataFactory=new s.StandardFontDataFactory({baseUrl:s.standardFontDataUrl})}this.destroyed=!1;this.destroyCapability=null;this._passwordCapability=null;this._networkStream=r;this._fullReader=null;this._lastProgress=null;this.pageCache=[];this.pagePromises=[];this.downloadInfoCapability=(0,_util.createPromiseCapability)();this.setupMessageHandler()}get annotationStorage(){return(0,_util.shadow)(this,"annotationStorage",new _annotation_storage.AnnotationStorage)}destroy(){if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0;this.destroyCapability=(0,_util.createPromiseCapability)();this._passwordCapability&&this._passwordCapability.reject(new Error("Worker was destroyed during onPassword callback"));const e=[];for(const t of this.pageCache)t&&e.push(t._destroy());this.pageCache.length=0;this.pagePromises.length=0;this.hasOwnProperty("annotationStorage")&&this.annotationStorage.resetModified();const t=this.messageHandler.sendWithPromise("Terminate",null);e.push(t);Promise.all(e).then((()=>{this.commonObjs.clear();this.fontLoader.clear();this._hasJSActionsPromise=null;this._networkStream&&this._networkStream.cancelAllRequests(new _util.AbortException("Worker was terminated."));if(this.messageHandler){this.messageHandler.destroy();this.messageHandler=null}this.destroyCapability.resolve()}),this.destroyCapability.reject);return this.destroyCapability.promise}setupMessageHandler(){const{messageHandler:e,loadingTask:t}=this;e.on("GetReader",((e,t)=>{(0,_util.assert)(this._networkStream,"GetReader - no `IPDFStream` instance available.");this._fullReader=this._networkStream.getFullReader();this._fullReader.onProgress=e=>{this._lastProgress={loaded:e.loaded,total:e.total}};t.onPull=()=>{this._fullReader.read().then((function({value:e,done:r}){if(r)t.close();else{(0,_util.assert)((0,_util.isArrayBuffer)(e),"GetReader - expected an ArrayBuffer.");t.enqueue(new Uint8Array(e),1,[e])}})).catch((e=>{t.error(e)}))};t.onCancel=e=>{this._fullReader.cancel(e);t.ready.catch((e=>{if(!this.destroyed)throw e}))}}));e.on("ReaderHeadersReady",(e=>{const r=(0,_util.createPromiseCapability)(),s=this._fullReader;s.headersReady.then((()=>{if(!s.isStreamingSupported||!s.isRangeSupported){this._lastProgress&&t.onProgress&&t.onProgress(this._lastProgress);s.onProgress=e=>{t.onProgress&&t.onProgress({loaded:e.loaded,total:e.total})}}r.resolve({isStreamingSupported:s.isStreamingSupported,isRangeSupported:s.isRangeSupported,contentLength:s.contentLength})}),r.reject);return r.promise}));e.on("GetRangeReader",((e,t)=>{(0,_util.assert)(this._networkStream,"GetRangeReader - no `IPDFStream` instance available.");const r=this._networkStream.getRangeReader(e.begin,e.end);if(r){t.onPull=()=>{r.read().then((function({value:e,done:r}){if(r)t.close();else{(0,_util.assert)((0,_util.isArrayBuffer)(e),"GetRangeReader - expected an ArrayBuffer.");t.enqueue(new Uint8Array(e),1,[e])}})).catch((e=>{t.error(e)}))};t.onCancel=e=>{r.cancel(e);t.ready.catch((e=>{if(!this.destroyed)throw e}))}}else t.close()}));e.on("GetDoc",(({pdfInfo:e})=>{this._numPages=e.numPages;this._htmlForXfa=e.htmlForXfa;delete e.htmlForXfa;t._capability.resolve(new PDFDocumentProxy(e,this))}));e.on("DocException",(function(e){let r;switch(e.name){case"PasswordException":r=new _util.PasswordException(e.message,e.code);break;case"InvalidPDFException":r=new _util.InvalidPDFException(e.message);break;case"MissingPDFException":r=new _util.MissingPDFException(e.message);break;case"UnexpectedResponseException":r=new _util.UnexpectedResponseException(e.message,e.status);break;case"UnknownErrorException":r=new _util.UnknownErrorException(e.message,e.details)}if(!(r instanceof Error)){const e="DocException - expected a valid Error.";(0,_util.warn)(e)}t._capability.reject(r)}));e.on("PasswordRequest",(e=>{this._passwordCapability=(0,_util.createPromiseCapability)();if(t.onPassword){const updatePassword=e=>{this._passwordCapability.resolve({password:e})};try{t.onPassword(updatePassword,e.code)}catch(e){this._passwordCapability.reject(e)}}else this._passwordCapability.reject(new _util.PasswordException(e.message,e.code));return this._passwordCapability.promise}));e.on("DataLoaded",(e=>{t.onProgress&&t.onProgress({loaded:e.length,total:e.length});this.downloadInfoCapability.resolve(e)}));e.on("StartRenderPage",(e=>{if(this.destroyed)return;this.pageCache[e.pageIndex]._startRenderPage(e.transparency,e.intent)}));e.on("commonobj",(t=>{if(this.destroyed)return;const[r,s,n]=t;if(!this.commonObjs.has(r))switch(s){case"Font":const t=this._params;if("error"in n){const e=n.error;(0,_util.warn)(`Error during font loading: ${e}`);this.commonObjs.resolve(r,e);break}let a=null;t.pdfBug&&globalThis.FontInspector?.enabled&&(a={registerFont(e,t){globalThis.FontInspector.fontAdded(e,t)}});const i=new _font_loader.FontFaceObject(n,{isEvalSupported:t.isEvalSupported,disableFontFace:t.disableFontFace,ignoreErrors:t.ignoreErrors,onUnsupportedFeature:this._onUnsupportedFeature.bind(this),fontRegistry:a});this.fontLoader.bind(i).catch((t=>e.sendWithPromise("FontFallback",{id:r}))).finally((()=>{!t.fontExtraProperties&&i.data&&(i.data=null);this.commonObjs.resolve(r,i)}));break;case"FontPath":case"Image":this.commonObjs.resolve(r,n);break;default:throw new Error(`Got unknown common object type ${s}`)}}));e.on("obj",(e=>{if(this.destroyed)return;const[t,r,s,n]=e,a=this.pageCache[r];if(!a.objs.has(t))switch(s){case"Image":a.objs.resolve(t,n);const e=8e6;n?.data?.length>e&&(a.cleanupAfterRender=!0);break;case"Pattern":a.objs.resolve(t,n);break;default:throw new Error(`Got unknown object type ${s}`)}}));e.on("DocProgress",(e=>{this.destroyed||t.onProgress&&t.onProgress({loaded:e.loaded,total:e.total})}));e.on("UnsupportedFeature",this._onUnsupportedFeature.bind(this));e.on("FetchBuiltInCMap",(e=>this.destroyed?Promise.reject(new Error("Worker was destroyed.")):this.CMapReaderFactory?this.CMapReaderFactory.fetch(e):Promise.reject(new Error("CMapReaderFactory not initialized, see the `useWorkerFetch` parameter."))));e.on("FetchStandardFontData",(e=>this.destroyed?Promise.reject(new Error("Worker was destroyed.")):this.StandardFontDataFactory?this.StandardFontDataFactory.fetch(e):Promise.reject(new Error("StandardFontDataFactory not initialized, see the `useWorkerFetch` parameter."))))}_onUnsupportedFeature({featureId:e}){this.destroyed||this.loadingTask.onUnsupportedFeature&&this.loadingTask.onUnsupportedFeature(e)}getData(){return this.messageHandler.sendWithPromise("GetData",null)}getPage(e){if(!Number.isInteger(e)||e<=0||e>this._numPages)return Promise.reject(new Error("Invalid page request"));const t=e-1;if(t in this.pagePromises)return this.pagePromises[t];const r=this.messageHandler.sendWithPromise("GetPage",{pageIndex:t}).then((e=>{if(this.destroyed)throw new Error("Transport destroyed");const r=new PDFPageProxy(t,e,this,this._params.ownerDocument,this._params.pdfBug);this.pageCache[t]=r;return r}));this.pagePromises[t]=r;return r}getPageIndex(e){return this.messageHandler.sendWithPromise("GetPageIndex",{ref:e}).catch((function(e){return Promise.reject(new Error(e))}))}getAnnotations(e,t){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:e,intent:t})}saveDocument(){return this.messageHandler.sendWithPromise("SaveDocument",{isPureXfa:!!this._htmlForXfa,numPages:this._numPages,annotationStorage:this.annotationStorage.serializable,filename:this._fullReader?.filename??null}).finally((()=>{this.annotationStorage.resetModified()}))}getFieldObjects(){return this.messageHandler.sendWithPromise("GetFieldObjects",null)}hasJSActions(){return this._hasJSActionsPromise||(this._hasJSActionsPromise=this.messageHandler.sendWithPromise("HasJSActions",null))}getCalculationOrderIds(){return this.messageHandler.sendWithPromise("GetCalculationOrderIds",null)}getDestinations(){return this.messageHandler.sendWithPromise("GetDestinations",null)}getDestination(e){return"string"!=typeof e?Promise.reject(new Error("Invalid destination request.")):this.messageHandler.sendWithPromise("GetDestination",{id:e})}getPageLabels(){return this.messageHandler.sendWithPromise("GetPageLabels",null)}getPageLayout(){return this.messageHandler.sendWithPromise("GetPageLayout",null)}getPageMode(){return this.messageHandler.sendWithPromise("GetPageMode",null)}getViewerPreferences(){return this.messageHandler.sendWithPromise("GetViewerPreferences",null)}getOpenAction(){return this.messageHandler.sendWithPromise("GetOpenAction",null)}getAttachments(){return this.messageHandler.sendWithPromise("GetAttachments",null)}getJavaScript(){return this.messageHandler.sendWithPromise("GetJavaScript",null)}getDocJSActions(){return this.messageHandler.sendWithPromise("GetDocJSActions",null)}getPageJSActions(e){return this.messageHandler.sendWithPromise("GetPageJSActions",{pageIndex:e})}getStructTree(e){return this.messageHandler.sendWithPromise("GetStructTree",{pageIndex:e})}getOutline(){return this.messageHandler.sendWithPromise("GetOutline",null)}getOptionalContentConfig(){return this.messageHandler.sendWithPromise("GetOptionalContentConfig",null).then((e=>new _optional_content_config.OptionalContentConfig(e)))}getPermissions(){return this.messageHandler.sendWithPromise("GetPermissions",null)}getMetadata(){return this.messageHandler.sendWithPromise("GetMetadata",null).then((e=>({info:e[0],metadata:e[1]?new _metadata.Metadata(e[1]):null,contentDispositionFilename:this._fullReader?.filename??null,contentLength:this._fullReader?.contentLength??null})))}getMarkInfo(){return this.messageHandler.sendWithPromise("GetMarkInfo",null)}getStats(){return this.messageHandler.sendWithPromise("GetStats",null)}async startCleanup(e=!1){await this.messageHandler.sendWithPromise("Cleanup",null);if(!this.destroyed){for(let e=0,t=this.pageCache.length;e<t;e++){const t=this.pageCache[e];if(!t)continue;if(!t.cleanup())throw new Error(`startCleanup: Page ${e+1} is currently rendering.`)}this.commonObjs.clear();e||this.fontLoader.clear();this._hasJSActionsPromise=null}}get loadingParams(){const e=this._params;return(0,_util.shadow)(this,"loadingParams",{disableAutoFetch:e.disableAutoFetch})}}class PDFObjects{constructor(){this._objs=Object.create(null)}_ensureObj(e){return this._objs[e]?this._objs[e]:this._objs[e]={capability:(0,_util.createPromiseCapability)(),data:null,resolved:!1}}get(e,t=null){if(t){this._ensureObj(e).capability.promise.then(t);return null}const r=this._objs[e];if(!r||!r.resolved)throw new Error(`Requesting object that isn't resolved yet ${e}.`);return r.data}has(e){return this._objs[e]?.resolved||!1}resolve(e,t){const r=this._ensureObj(e);r.resolved=!0;r.data=t;r.capability.resolve(t)}clear(){this._objs=Object.create(null)}}class RenderTask{constructor(e){this._internalRenderTask=e;this.onContinue=null}get promise(){return this._internalRenderTask.capability.promise}cancel(){this._internalRenderTask.cancel()}}const InternalRenderTask=function InternalRenderTaskClosure(){const e=new WeakSet;return class InternalRenderTask{constructor({callback:e,params:t,objs:r,commonObjs:s,operatorList:n,pageIndex:a,canvasFactory:i,useRequestAnimationFrame:o=!1,pdfBug:l=!1}){this.callback=e;this.params=t;this.objs=r;this.commonObjs=s;this.operatorListIdx=null;this.operatorList=n;this._pageIndex=a;this.canvasFactory=i;this._pdfBug=l;this.running=!1;this.graphicsReadyCallback=null;this.graphicsReady=!1;this._useRequestAnimationFrame=!0===o&&"undefined"!=typeof window;this.cancelled=!1;this.capability=(0,_util.createPromiseCapability)();this.task=new RenderTask(this);this._cancelBound=this.cancel.bind(this);this._continueBound=this._continue.bind(this);this._scheduleNextBound=this._scheduleNext.bind(this);this._nextBound=this._next.bind(this);this._canvas=t.canvasContext.canvas}get completed(){return this.capability.promise.catch((function(){}))}initializeGraphics({transparency:t=!1,optionalContentConfig:r}){if(this.cancelled)return;if(this._canvas){if(e.has(this._canvas))throw new Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");e.add(this._canvas)}if(this._pdfBug&&globalThis.StepperManager?.enabled){this.stepper=globalThis.StepperManager.create(this._pageIndex);this.stepper.init(this.operatorList);this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint()}const{canvasContext:s,viewport:n,transform:a,imageLayer:i,background:o}=this.params;this.gfx=new _canvas.CanvasGraphics(s,this.commonObjs,this.objs,this.canvasFactory,i,r);this.gfx.beginDrawing({transform:a,viewport:n,transparency:t,background:o});this.operatorListIdx=0;this.graphicsReady=!0;this.graphicsReadyCallback&&this.graphicsReadyCallback()}cancel(t=null){this.running=!1;this.cancelled=!0;this.gfx&&this.gfx.endDrawing();this._canvas&&e.delete(this._canvas);this.callback(t||new _display_utils.RenderingCancelledException(`Rendering cancelled, page ${this._pageIndex+1}`,"canvas"))}operatorListChanged(){if(this.graphicsReady){this.stepper&&this.stepper.updateOperatorList(this.operatorList);this.running||this._continue()}else this.graphicsReadyCallback||(this.graphicsReadyCallback=this._continueBound)}_continue(){this.running=!0;this.cancelled||(this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext())}_scheduleNext(){this._useRequestAnimationFrame?window.requestAnimationFrame((()=>{this._nextBound().catch(this._cancelBound)})):Promise.resolve().then(this._nextBound).catch(this._cancelBound)}async _next(){if(!this.cancelled){this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper);if(this.operatorListIdx===this.operatorList.argsArray.length){this.running=!1;if(this.operatorList.lastChunk){this.gfx.endDrawing();this._canvas&&e.delete(this._canvas);this.callback()}}}}}}(),version="2.10.377";exports.version=version;const build="156762c48";exports.build=build},(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});t.FontLoader=t.FontFaceObject=void 0;var s=r(2);class BaseFontLoader{constructor({docId:e,onUnsupportedFeature:t,ownerDocument:r=globalThis.document,styleElement:n=null}){this.constructor===BaseFontLoader&&(0,s.unreachable)("Cannot initialize BaseFontLoader.");this.docId=e;this._onUnsupportedFeature=t;this._document=r;this.nativeFontFaces=[];this.styleElement=null}addNativeFontFace(e){this.nativeFontFaces.push(e);this._document.fonts.add(e)}insertRule(e){let t=this.styleElement;if(!t){t=this.styleElement=this._document.createElement("style");t.id=`PDFJS_FONT_STYLE_TAG_${this.docId}`;this._document.documentElement.getElementsByTagName("head")[0].appendChild(t)}const r=t.sheet;r.insertRule(e,r.cssRules.length)}clear(){for(const e of this.nativeFontFaces)this._document.fonts.delete(e);this.nativeFontFaces.length=0;if(this.styleElement){this.styleElement.remove();this.styleElement=null}}async bind(e){if(e.attached||e.missingFile)return;e.attached=!0;if(this.isFontLoadingAPISupported){const t=e.createNativeFontFace();if(t){this.addNativeFontFace(t);try{await t.loaded}catch(r){this._onUnsupportedFeature({featureId:s.UNSUPPORTED_FEATURES.errorFontLoadNative});(0,s.warn)(`Failed to load font '${t.family}': '${r}'.`);e.disableFontFace=!0;throw r}}return}const t=e.createFontFaceRule();if(t){this.insertRule(t);if(this.isSyncFontLoadingSupported)return;await new Promise((r=>{const s=this._queueLoadingCallback(r);this._prepareFontLoadEvent([t],[e],s)}))}}_queueLoadingCallback(e){(0,s.unreachable)("Abstract method `_queueLoadingCallback`.")}get isFontLoadingAPISupported(){const e=!!this._document?.fonts;return(0,s.shadow)(this,"isFontLoadingAPISupported",e)}get isSyncFontLoadingSupported(){(0,s.unreachable)("Abstract method `isSyncFontLoadingSupported`.")}get _loadTestFont(){(0,s.unreachable)("Abstract method `_loadTestFont`.")}_prepareFontLoadEvent(e,t,r){(0,s.unreachable)("Abstract method `_prepareFontLoadEvent`.")}}let n;t.FontLoader=n;t.FontLoader=n=class GenericFontLoader extends BaseFontLoader{constructor(e){super(e);this.loadingContext={requests:[],nextRequestId:0};this.loadTestFontId=0}get isSyncFontLoadingSupported(){let e=!1;if("undefined"==typeof navigator)e=!0;else{/Mozilla\/5.0.*?rv:(\d+).*? Gecko/.exec(navigator.userAgent)?.[1]>=14&&(e=!0)}return(0,s.shadow)(this,"isSyncFontLoadingSupported",e)}_queueLoadingCallback(e){const t=this.loadingContext,r={id:"pdfjs-font-loading-"+t.nextRequestId++,done:!1,complete:function completeRequest(){(0,s.assert)(!r.done,"completeRequest() cannot be called twice.");r.done=!0;for(;t.requests.length>0&&t.requests[0].done;){const e=t.requests.shift();setTimeout(e.callback,0)}},callback:e};t.requests.push(r);return r}get _loadTestFont(){return(0,s.shadow)(this,"_loadTestFont",atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA=="))}_prepareFontLoadEvent(e,t,r){function int32(e,t){return e.charCodeAt(t)<<24|e.charCodeAt(t+1)<<16|e.charCodeAt(t+2)<<8|255&e.charCodeAt(t+3)}function spliceString(e,t,r,s){return e.substring(0,t)+s+e.substring(t+r)}let n,a;const i=this._document.createElement("canvas");i.width=1;i.height=1;const o=i.getContext("2d");let l=0;const c=`lt${Date.now()}${this.loadTestFontId++}`;let h=this._loadTestFont;h=spliceString(h,976,c.length,c);const d=1482184792;let u=int32(h,16);for(n=0,a=c.length-3;n<a;n+=4)u=u-d+int32(c,n)|0;n<c.length&&(u=u-d+int32(c+"XXX",n)|0);h=spliceString(h,16,4,(0,s.string32)(u));const p=`@font-face {font-family:"${c}";src:${`url(data:font/opentype;base64,${btoa(h)});`}}`;this.insertRule(p);const g=[];for(const e of t)g.push(e.loadedName);g.push(c);const f=this._document.createElement("div");f.style.visibility="hidden";f.style.width=f.style.height="10px";f.style.position="absolute";f.style.top=f.style.left="0px";for(const e of g){const t=this._document.createElement("span");t.textContent="Hi";t.style.fontFamily=e;f.appendChild(t)}this._document.body.appendChild(f);!function isFontReady(e,t){l++;if(l>30){(0,s.warn)("Load test font never loaded.");t();return}o.font="30px "+e;o.fillText(".",0,20);o.getImageData(0,0,1,1).data[3]>0?t():setTimeout(isFontReady.bind(null,e,t))}(c,(()=>{this._document.body.removeChild(f);r.complete()}))}};t.FontFaceObject=class FontFaceObject{constructor(e,{isEvalSupported:t=!0,disableFontFace:r=!1,ignoreErrors:s=!1,onUnsupportedFeature:n,fontRegistry:a=null}){this.compiledGlyphs=Object.create(null);for(const t in e)this[t]=e[t];this.isEvalSupported=!1!==t;this.disableFontFace=!0===r;this.ignoreErrors=!0===s;this._onUnsupportedFeature=n;this.fontRegistry=a}createNativeFontFace(){if(!this.data||this.disableFontFace)return null;let e;if(this.cssFontInfo){const t={weight:this.cssFontInfo.fontWeight};this.cssFontInfo.italicAngle&&(t.style=`oblique ${this.cssFontInfo.italicAngle}deg`);e=new FontFace(this.cssFontInfo.fontFamily,this.data,t)}else e=new FontFace(this.loadedName,this.data,{});this.fontRegistry&&this.fontRegistry.registerFont(this);return e}createFontFaceRule(){if(!this.data||this.disableFontFace)return null;const e=(0,s.bytesToString)(this.data),t=`url(data:${this.mimetype};base64,${btoa(e)});`;let r;if(this.cssFontInfo){let e=`font-weight: ${this.cssFontInfo.fontWeight};`;this.cssFontInfo.italicAngle&&(e+=`font-style: oblique ${this.cssFontInfo.italicAngle}deg;`);r=`@font-face {font-family:"${this.cssFontInfo.fontFamily}";${e}src:${t}}`}else r=`@font-face {font-family:"${this.loadedName}";src:${t}}`;this.fontRegistry&&this.fontRegistry.registerFont(this,t);return r}getPathGenerator(e,t){if(void 0!==this.compiledGlyphs[t])return this.compiledGlyphs[t];let r;try{r=e.get(this.loadedName+"_path_"+t)}catch(e){if(!this.ignoreErrors)throw e;this._onUnsupportedFeature({featureId:s.UNSUPPORTED_FEATURES.errorFontGetPath});(0,s.warn)(`getPathGenerator - ignoring character: "${e}".`);return this.compiledGlyphs[t]=function(e,t){}}if(this.isEvalSupported&&s.IsEvalSupportedCached.value){const e=[];for(const t of r){const r=void 0!==t.args?t.args.join(","):"";e.push("c.",t.cmd,"(",r,");\n")}return this.compiledGlyphs[t]=new Function("c","size",e.join(""))}return this.compiledGlyphs[t]=function(e,t){for(const s of r){"scale"===s.cmd&&(s.args=[t,-t]);e[s.cmd].apply(e,s.args)}}}}},(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});t.NodeStandardFontDataFactory=t.NodeCMapReaderFactory=t.NodeCanvasFactory=void 0;var s=r(5),n=r(4),a=r(2);let i=class{constructor(){(0,a.unreachable)("Not implemented: NodeCanvasFactory")}};t.NodeCanvasFactory=i;let o=class{constructor(){(0,a.unreachable)("Not implemented: NodeCMapReaderFactory")}};t.NodeCMapReaderFactory=o;let l=class{constructor(){(0,a.unreachable)("Not implemented: NodeStandardFontDataFactory")}};t.NodeStandardFontDataFactory=l;if(n.isNodeJS){const fetchData=function(e){return new Promise(((t,r)=>{require("fs").readFile(e,((e,s)=>{!e&&s?t(new Uint8Array(s)):r(new Error(e))}))}))};t.NodeCanvasFactory=i=class extends s.BaseCanvasFactory{_createCanvas(e,t){return require("canvas").createCanvas(e,t)}};t.NodeCMapReaderFactory=o=class extends s.BaseCMapReaderFactory{_fetchData(e,t){return fetchData(e).then((e=>({cMapData:e,compressionType:t})))}};t.NodeStandardFontDataFactory=l=class extends s.BaseStandardFontDataFactory{_fetchData(e){return fetchData(e)}}}},(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});t.AnnotationStorage=void 0;var s=r(2);t.AnnotationStorage=class AnnotationStorage{constructor(){this._storage=new Map;this._modified=!1;this.onSetModified=null;this.onResetModified=null}getValue(e,t){const r=this._storage.get(e);return void 0!==r?r:t}setValue(e,t){const r=this._storage.get(e);let s=!1;if(void 0!==r){for(const[e,n]of Object.entries(t))if(r[e]!==n){s=!0;r[e]=n}}else{this._storage.set(e,t);s=!0}s&&this._setModified()}getAll(){return this._storage.size>0?(0,s.objectFromMap)(this._storage):null}get size(){return this._storage.size}_setModified(){if(!this._modified){this._modified=!0;"function"==typeof this.onSetModified&&this.onSetModified()}}resetModified(){if(this._modified){this._modified=!1;"function"==typeof this.onResetModified&&this.onResetModified()}}get serializable(){return this._storage.size>0?this._storage:null}}},(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});t.CanvasGraphics=void 0;var s=r(2),n=r(11);const a=4096,i=16;function addContextCurrentTransform(e){if(!e.mozCurrentTransform){e._originalSave=e.save;e._originalRestore=e.restore;e._originalRotate=e.rotate;e._originalScale=e.scale;e._originalTranslate=e.translate;e._originalTransform=e.transform;e._originalSetTransform=e.setTransform;e._originalResetTransform=e.resetTransform;e._transformMatrix=e._transformMatrix||[1,0,0,1,0,0];e._transformStack=[];try{const t=Object.getOwnPropertyDescriptor(Object.getPrototypeOf(e),"lineWidth");e._setLineWidth=t.set;e._getLineWidth=t.get;Object.defineProperty(e,"lineWidth",{set:function setLineWidth(e){this._setLineWidth(1.000001*e)},get:function getLineWidth(){return this._getLineWidth()}})}catch(e){}Object.defineProperty(e,"mozCurrentTransform",{get:function getCurrentTransform(){return this._transformMatrix}});Object.defineProperty(e,"mozCurrentTransformInverse",{get:function getCurrentTransformInverse(){const[e,t,r,s,n,a]=this._transformMatrix,i=e*s-t*r,o=t*r-e*s;return[s/i,t/o,r/o,e/i,(s*n-r*a)/o,(t*n-e*a)/i]}});e.save=function ctxSave(){const e=this._transformMatrix;this._transformStack.push(e);this._transformMatrix=e.slice(0,6);this._originalSave()};e.restore=function ctxRestore(){const e=this._transformStack.pop();if(e){this._transformMatrix=e;this._originalRestore()}};e.translate=function ctxTranslate(e,t){const r=this._transformMatrix;r[4]=r[0]*e+r[2]*t+r[4];r[5]=r[1]*e+r[3]*t+r[5];this._originalTranslate(e,t)};e.scale=function ctxScale(e,t){const r=this._transformMatrix;r[0]*=e;r[1]*=e;r[2]*=t;r[3]*=t;this._originalScale(e,t)};e.transform=function ctxTransform(t,r,s,n,a,i){const o=this._transformMatrix;this._transformMatrix=[o[0]*t+o[2]*r,o[1]*t+o[3]*r,o[0]*s+o[2]*n,o[1]*s+o[3]*n,o[0]*a+o[2]*i+o[4],o[1]*a+o[3]*i+o[5]];e._originalTransform(t,r,s,n,a,i)};e.setTransform=function ctxSetTransform(t,r,s,n,a,i){this._transformMatrix=[t,r,s,n,a,i];e._originalSetTransform(t,r,s,n,a,i)};e.resetTransform=function ctxResetTransform(){this._transformMatrix=[1,0,0,1,0,0];e._originalResetTransform()};e.rotate=function ctxRotate(e){const t=Math.cos(e),r=Math.sin(e),s=this._transformMatrix;this._transformMatrix=[s[0]*t+s[2]*r,s[1]*t+s[3]*r,s[0]*-r+s[2]*t,s[1]*-r+s[3]*t,s[4],s[5]];this._originalRotate(e)}}}class CachedCanvases{constructor(e){this.canvasFactory=e;this.cache=Object.create(null)}getCanvas(e,t,r,s){let n;if(void 0!==this.cache[e]){n=this.cache[e];this.canvasFactory.reset(n,t,r);n.context.setTransform(1,0,0,1,0,0)}else{n=this.canvasFactory.create(t,r);this.cache[e]=n}s&&addContextCurrentTransform(n.context);return n}clear(){for(const e in this.cache){const t=this.cache[e];this.canvasFactory.destroy(t);delete this.cache[e]}}}class CanvasExtraState{constructor(){this.alphaIsShape=!1;this.fontSize=0;this.fontSizeScale=1;this.textMatrix=s.IDENTITY_MATRIX;this.textMatrixScale=1;this.fontMatrix=s.FONT_IDENTITY_MATRIX;this.leading=0;this.x=0;this.y=0;this.lineX=0;this.lineY=0;this.charSpacing=0;this.wordSpacing=0;this.textHScale=1;this.textRenderingMode=s.TextRenderingMode.FILL;this.textRise=0;this.fillColor="#000000";this.strokeColor="#000000";this.patternFill=!1;this.fillAlpha=1;this.strokeAlpha=1;this.lineWidth=1;this.activeSMask=null;this.resumeSMaskCtx=null;this.transferMaps=null}clone(){return Object.create(this)}setCurrentPoint(e,t){this.x=e;this.y=t}}const o=function CanvasGraphicsClosure(){function putBinaryImageData(e,t,r=null){if("undefined"!=typeof ImageData&&t instanceof ImageData){e.putImageData(t,0,0);return}const n=t.height,a=t.width,o=n%i,l=(n-o)/i,c=0===o?l:l+1,h=e.createImageData(a,i);let d,u=0;const p=t.data,g=h.data;let f,m,A,_,b,y,S,v;if(r)switch(r.length){case 1:b=r[0];y=r[0];S=r[0];v=r[0];break;case 4:b=r[0];y=r[1];S=r[2];v=r[3]}if(t.kind===s.ImageKind.GRAYSCALE_1BPP){const t=p.byteLength,r=new Uint32Array(g.buffer,0,g.byteLength>>2),n=r.length,_=a+7>>3;let b=4294967295,y=s.IsLittleEndianCached.value?4278190080:255;v&&255===v[0]&&0===v[255]&&([b,y]=[y,b]);for(f=0;f<c;f++){A=f<l?i:o;d=0;for(m=0;m<A;m++){const e=t-u;let s=0;const n=e>_?a:8*e-7,i=-8&n;let o=0,l=0;for(;s<i;s+=8){l=p[u++];r[d++]=128&l?b:y;r[d++]=64&l?b:y;r[d++]=32&l?b:y;r[d++]=16&l?b:y;r[d++]=8&l?b:y;r[d++]=4&l?b:y;r[d++]=2&l?b:y;r[d++]=1&l?b:y}for(;s<n;s++){if(0===o){l=p[u++];o=128}r[d++]=l&o?b:y;o>>=1}}for(;d<n;)r[d++]=0;e.putImageData(h,0,f*i)}}else if(t.kind===s.ImageKind.RGBA_32BPP){const t=!!(b||y||S);m=0;_=a*i*4;for(f=0;f<l;f++){g.set(p.subarray(u,u+_));u+=_;if(t)for(let e=0;e<_;e+=4){b&&(g[e+0]=b[g[e+0]]);y&&(g[e+1]=y[g[e+1]]);S&&(g[e+2]=S[g[e+2]])}e.putImageData(h,0,m);m+=i}if(f<c){_=a*o*4;g.set(p.subarray(u,u+_));if(t)for(let e=0;e<_;e+=4){b&&(g[e+0]=b[g[e+0]]);y&&(g[e+1]=y[g[e+1]]);S&&(g[e+2]=S[g[e+2]])}e.putImageData(h,0,m)}}else{if(t.kind!==s.ImageKind.RGB_24BPP)throw new Error(`bad image kind: ${t.kind}`);{const t=!!(b||y||S);A=i;_=a*A;for(f=0;f<c;f++){if(f>=l){A=o;_=a*A}d=0;for(m=_;m--;){g[d++]=p[u++];g[d++]=p[u++];g[d++]=p[u++];g[d++]=255}if(t)for(let e=0;e<d;e+=4){b&&(g[e+0]=b[g[e+0]]);y&&(g[e+1]=y[g[e+1]]);S&&(g[e+2]=S[g[e+2]])}e.putImageData(h,0,f*i)}}}}function putBinaryImageMask(e,t){const r=t.height,s=t.width,n=r%i,a=(r-n)/i,o=0===n?a:a+1,l=e.createImageData(s,i);let c=0;const h=t.data,d=l.data;for(let t=0;t<o;t++){const r=t<a?i:n;let o=3;for(let e=0;e<r;e++){let e,t=0;for(let r=0;r<s;r++){if(!t){e=h[c++];t=128}d[o]=e&t?0:255;o+=4;t>>=1}}e.putImageData(l,0,t*i)}}function copyCtxState(e,t){const r=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font"];for(let s=0,n=r.length;s<n;s++){const n=r[s];void 0!==e[n]&&(t[n]=e[n])}if(void 0!==e.setLineDash){t.setLineDash(e.getLineDash());t.lineDashOffset=e.lineDashOffset}}function resetCtxToDefault(e){e.strokeStyle="#000000";e.fillStyle="#000000";e.fillRule="nonzero";e.globalAlpha=1;e.lineWidth=1;e.lineCap="butt";e.lineJoin="miter";e.miterLimit=10;e.globalCompositeOperation="source-over";e.font="10px sans-serif";if(void 0!==e.setLineDash){e.setLineDash([]);e.lineDashOffset=0}}function composeSMaskBackdrop(e,t,r,s){const n=e.length;for(let a=3;a<n;a+=4){const n=e[a];if(0===n){e[a-3]=t;e[a-2]=r;e[a-1]=s}else if(n<255){const i=255-n;e[a-3]=e[a-3]*n+t*i>>8;e[a-2]=e[a-2]*n+r*i>>8;e[a-1]=e[a-1]*n+s*i>>8}}}function composeSMaskAlpha(e,t,r){const s=e.length;for(let n=3;n<s;n+=4){const s=r?r[e[n]]:e[n];t[n]=t[n]*s*.00392156862745098|0}}function composeSMaskLuminosity(e,t,r){const s=e.length;for(let n=3;n<s;n+=4){const s=77*e[n-3]+152*e[n-2]+28*e[n-1];t[n]=r?t[n]*r[s>>8]>>8:t[n]*s>>16}}function composeSMask(e,t,r){const s=t.canvas,n=t.context;e.setTransform(t.scaleX,0,0,t.scaleY,t.offsetX,t.offsetY);!function genericComposeSMask(e,t,r,s,n,a,i){const o=!!a,l=o?a[0]:0,c=o?a[1]:0,h=o?a[2]:0;let d;d="Luminosity"===n?composeSMaskLuminosity:composeSMaskAlpha;const u=Math.min(s,Math.ceil(1048576/r));for(let n=0;n<s;n+=u){const a=Math.min(u,s-n),p=e.getImageData(0,n,r,a),g=t.getImageData(0,n,r,a);o&&composeSMaskBackdrop(p.data,l,c,h);d(p.data,g.data,i);e.putImageData(g,0,n)}}(n,r,s.width,s.height,t.subtype,t.backdrop,t.transferMap);e.drawImage(s,0,0)}const e=["butt","round","square"],t=["miter","round","bevel"],r={},o={};class CanvasGraphics{constructor(e,t,r,s,n,a){this.ctx=e;this.current=new CanvasExtraState;this.stateStack=[];this.pendingClip=null;this.pendingEOFill=!1;this.res=null;this.xobjs=null;this.commonObjs=t;this.objs=r;this.canvasFactory=s;this.imageLayer=n;this.groupStack=[];this.processingType3=null;this.baseTransform=null;this.baseTransformStack=[];this.groupLevel=0;this.smaskStack=[];this.smaskCounter=0;this.tempSMask=null;this.contentVisible=!0;this.markedContentStack=[];this.optionalContentConfig=a;this.cachedCanvases=new CachedCanvases(this.canvasFactory);this.cachedPatterns=new Map;e&&addContextCurrentTransform(e);this._cachedGetSinglePixelWidth=null}beginDrawing({transform:e,viewport:t,transparency:r=!1,background:s=null}){const n=this.ctx.canvas.width,a=this.ctx.canvas.height;this.ctx.save();this.ctx.fillStyle=s||"rgb(255, 255, 255)";this.ctx.fillRect(0,0,n,a);this.ctx.restore();if(r){const e=this.cachedCanvases.getCanvas("transparent",n,a,!0);this.compositeCtx=this.ctx;this.transparentCanvas=e.canvas;this.ctx=e.context;this.ctx.save();this.ctx.transform.apply(this.ctx,this.compositeCtx.mozCurrentTransform)}this.ctx.save();resetCtxToDefault(this.ctx);e&&this.ctx.transform.apply(this.ctx,e);this.ctx.transform.apply(this.ctx,t.transform);this.baseTransform=this.ctx.mozCurrentTransform.slice();this._combinedScaleFactor=Math.hypot(this.baseTransform[0],this.baseTransform[2]);this.imageLayer&&this.imageLayer.beginLayout()}executeOperatorList(e,t,r,n){const a=e.argsArray,i=e.fnArray;let o=t||0;const l=a.length;if(l===o)return o;const c=l-o>10&&"function"==typeof r,h=c?Date.now()+15:0;let d=0;const u=this.commonObjs,p=this.objs;let g;for(;;){if(void 0!==n&&o===n.nextBreakPoint){n.breakIt(o,r);return o}g=i[o];if(g!==s.OPS.dependency)this[g].apply(this,a[o]);else for(const e of a[o]){const t=e.startsWith("g_")?u:p;if(!t.has(e)){t.get(e,r);return o}}o++;if(o===l)return o;if(c&&++d>10){if(Date.now()>h){r();return o}d=0}}}endDrawing(){for(;this.stateStack.length||null!==this.current.activeSMask;)this.restore();this.ctx.restore();if(this.transparentCanvas){this.ctx=this.compositeCtx;this.ctx.save();this.ctx.setTransform(1,0,0,1,0,0);this.ctx.drawImage(this.transparentCanvas,0,0);this.ctx.restore();this.transparentCanvas=null}this.cachedCanvases.clear();this.cachedPatterns.clear();this.imageLayer&&this.imageLayer.endLayout()}_scaleImage(e,t){const r=e.width,s=e.height;let n,a,i=Math.max(Math.hypot(t[0],t[1]),1),o=Math.max(Math.hypot(t[2],t[3]),1),l=r,c=s,h="prescale1";for(;i>2&&l>1||o>2&&c>1;){let t=l,r=c;if(i>2&&l>1){t=Math.ceil(l/2);i/=l/t}if(o>2&&c>1){r=Math.ceil(c/2);o/=c/r}n=this.cachedCanvases.getCanvas(h,t,r);a=n.context;a.clearRect(0,0,t,r);a.drawImage(e,0,0,l,c,0,0,t,r);e=n.canvas;l=t;c=r;h="prescale1"===h?"prescale2":"prescale1"}return{img:e,paintWidth:l,paintHeight:c}}_createMaskCanvas(e){const t=this.ctx,r=e.width,n=e.height,a=this.current.fillColor,i=this.current.patternFill,o=this.cachedCanvases.getCanvas("maskCanvas",r,n);putBinaryImageMask(o.context,e);const l=t.mozCurrentTransform;let c=s.Util.transform(l,[1/r,0,0,-1/n,0,0]);c=s.Util.transform(c,[1,0,0,1,0,-n]);const h=s.Util.applyTransform([0,0],c),d=s.Util.applyTransform([r,n],c),u=s.Util.normalizeRect([h[0],h[1],d[0],d[1]]),p=Math.ceil(u[2]-u[0]),g=Math.ceil(u[3]-u[1]),f=this.cachedCanvases.getCanvas("fillCanvas",p,g,!0),m=f.context,A=Math.min(h[0],d[0]),_=Math.min(h[1],d[1]);m.translate(-A,-_);m.transform.apply(m,c);const b=this._scaleImage(o.canvas,m.mozCurrentTransformInverse);m.drawImage(b.img,0,0,b.img.width,b.img.height,0,0,r,n);m.globalCompositeOperation="source-in";const y=s.Util.transform(m.mozCurrentTransformInverse,[1,0,0,1,-A,-_]);m.fillStyle=i?a.getPattern(t,this,y,!1):a;m.fillRect(0,0,r,n);return{canvas:f.canvas,offsetX:Math.round(A),offsetY:Math.round(_)}}setLineWidth(e){this.current.lineWidth=e;this.ctx.lineWidth=e}setLineCap(t){this.ctx.lineCap=e[t]}setLineJoin(e){this.ctx.lineJoin=t[e]}setMiterLimit(e){this.ctx.miterLimit=e}setDash(e,t){const r=this.ctx;if(void 0!==r.setLineDash){r.setLineDash(e);r.lineDashOffset=t}}setRenderingIntent(e){}setFlatness(e){}setGState(e){for(let t=0,r=e.length;t<r;t++){const r=e[t],s=r[0],n=r[1];switch(s){case"LW":this.setLineWidth(n);break;case"LC":this.setLineCap(n);break;case"LJ":this.setLineJoin(n);break;case"ML":this.setMiterLimit(n);break;case"D":this.setDash(n[0],n[1]);break;case"RI":this.setRenderingIntent(n);break;case"FL":this.setFlatness(n);break;case"Font":this.setFont(n[0],n[1]);break;case"CA":this.current.strokeAlpha=r[1];break;case"ca":this.current.fillAlpha=r[1];this.ctx.globalAlpha=r[1];break;case"BM":this.ctx.globalCompositeOperation=n;break;case"SMask":this.current.activeSMask&&(this.stateStack.length>0&&this.stateStack[this.stateStack.length-1].activeSMask===this.current.activeSMask?this.suspendSMaskGroup():this.endSMaskGroup());this.current.activeSMask=n?this.tempSMask:null;this.current.activeSMask&&this.beginSMaskGroup();this.tempSMask=null;break;case"TR":this.current.transferMaps=n}}}beginSMaskGroup(){const e=this.current.activeSMask,t=e.canvas.width,r=e.canvas.height,s="smaskGroupAt"+this.groupLevel,n=this.cachedCanvases.getCanvas(s,t,r,!0),a=this.ctx,i=a.mozCurrentTransform;this.ctx.save();const o=n.context;o.scale(1/e.scaleX,1/e.scaleY);o.translate(-e.offsetX,-e.offsetY);o.transform.apply(o,i);e.startTransformInverse=o.mozCurrentTransformInverse;copyCtxState(a,o);this.ctx=o;this.setGState([["BM","source-over"],["ca",1],["CA",1]]);this.groupStack.push(a);this.groupLevel++}suspendSMaskGroup(){const e=this.ctx;this.groupLevel--;this.ctx=this.groupStack.pop();composeSMask(this.ctx,this.current.activeSMask,e);this.ctx.restore();this.ctx.save();copyCtxState(e,this.ctx);this.current.resumeSMaskCtx=e;const t=s.Util.transform(this.current.activeSMask.startTransformInverse,e.mozCurrentTransform);this.ctx.transform.apply(this.ctx,t);e.save();e.setTransform(1,0,0,1,0,0);e.clearRect(0,0,e.canvas.width,e.canvas.height);e.restore()}resumeSMaskGroup(){const e=this.current.resumeSMaskCtx,t=this.ctx;this.ctx=e;this.groupStack.push(t);this.groupLevel++}endSMaskGroup(){const e=this.ctx;this.groupLevel--;this.ctx=this.groupStack.pop();composeSMask(this.ctx,this.current.activeSMask,e);this.ctx.restore();copyCtxState(e,this.ctx);const t=s.Util.transform(this.current.activeSMask.startTransformInverse,e.mozCurrentTransform);this.ctx.transform.apply(this.ctx,t)}save(){this.ctx.save();const e=this.current;this.stateStack.push(e);this.current=e.clone();this.current.resumeSMaskCtx=null}restore(){this.current.resumeSMaskCtx&&this.resumeSMaskGroup();null===this.current.activeSMask||0!==this.stateStack.length&&this.stateStack[this.stateStack.length-1].activeSMask===this.current.activeSMask||this.endSMaskGroup();if(0!==this.stateStack.length){this.current=this.stateStack.pop();this.ctx.restore();this.pendingClip=null;this._cachedGetSinglePixelWidth=null}else this.current.activeSMask=null}transform(e,t,r,s,n,a){this.ctx.transform(e,t,r,s,n,a);this._cachedGetSinglePixelWidth=null}constructPath(e,t){const r=this.ctx,n=this.current;let a=n.x,i=n.y;for(let n=0,o=0,l=e.length;n<l;n++)switch(0|e[n]){case s.OPS.rectangle:a=t[o++];i=t[o++];const e=t[o++],n=t[o++],l=a+e,c=i+n;r.moveTo(a,i);if(0===e||0===n)r.lineTo(l,c);else{r.lineTo(l,i);r.lineTo(l,c);r.lineTo(a,c)}r.closePath();break;case s.OPS.moveTo:a=t[o++];i=t[o++];r.moveTo(a,i);break;case s.OPS.lineTo:a=t[o++];i=t[o++];r.lineTo(a,i);break;case s.OPS.curveTo:a=t[o+4];i=t[o+5];r.bezierCurveTo(t[o],t[o+1],t[o+2],t[o+3],a,i);o+=6;break;case s.OPS.curveTo2:r.bezierCurveTo(a,i,t[o],t[o+1],t[o+2],t[o+3]);a=t[o+2];i=t[o+3];o+=4;break;case s.OPS.curveTo3:a=t[o+2];i=t[o+3];r.bezierCurveTo(t[o],t[o+1],a,i,a,i);o+=4;break;case s.OPS.closePath:r.closePath()}n.setCurrentPoint(a,i)}closePath(){this.ctx.closePath()}stroke(e){e=void 0===e||e;const t=this.ctx,r=this.current.strokeColor;t.globalAlpha=this.current.strokeAlpha;if(this.contentVisible)if("object"==typeof r&&r?.getPattern){const e=this.getSinglePixelWidth();t.save();t.strokeStyle=r.getPattern(t,this,t.mozCurrentTransformInverse);t.lineWidth=Math.max(e,this.current.lineWidth);t.stroke();t.restore()}else{const e=this.getSinglePixelWidth();if(e<0&&-e>=this.current.lineWidth){t.save();t.resetTransform();t.lineWidth=Math.round(this._combinedScaleFactor);t.stroke();t.restore()}else{t.lineWidth=Math.max(e,this.current.lineWidth);t.stroke()}}e&&this.consumePath();t.globalAlpha=this.current.fillAlpha}closeStroke(){this.closePath();this.stroke()}fill(e){e=void 0===e||e;const t=this.ctx,r=this.current.fillColor;let s=!1;if(this.current.patternFill){t.save();t.fillStyle=r.getPattern(t,this,t.mozCurrentTransformInverse);s=!0}if(this.contentVisible)if(this.pendingEOFill){t.fill("evenodd");this.pendingEOFill=!1}else t.fill();s&&t.restore();e&&this.consumePath()}eoFill(){this.pendingEOFill=!0;this.fill()}fillStroke(){this.fill(!1);this.stroke(!1);this.consumePath()}eoFillStroke(){this.pendingEOFill=!0;this.fillStroke()}closeFillStroke(){this.closePath();this.fillStroke()}closeEOFillStroke(){this.pendingEOFill=!0;this.closePath();this.fillStroke()}endPath(){this.consumePath()}clip(){this.pendingClip=r}eoClip(){this.pendingClip=o}beginText(){this.current.textMatrix=s.IDENTITY_MATRIX;this.current.textMatrixScale=1;this.current.x=this.current.lineX=0;this.current.y=this.current.lineY=0}endText(){const e=this.pendingTextPaths,t=this.ctx;if(void 0!==e){t.save();t.beginPath();for(let r=0;r<e.length;r++){const s=e[r];t.setTransform.apply(t,s.transform);t.translate(s.x,s.y);s.addToPath(t,s.fontSize)}t.restore();t.clip();t.beginPath();delete this.pendingTextPaths}else t.beginPath()}setCharSpacing(e){this.current.charSpacing=e}setWordSpacing(e){this.current.wordSpacing=e}setHScale(e){this.current.textHScale=e/100}setLeading(e){this.current.leading=-e}setFont(e,t){const r=this.commonObjs.get(e),n=this.current;if(!r)throw new Error(`Can't find font for ${e}`);n.fontMatrix=r.fontMatrix||s.FONT_IDENTITY_MATRIX;0!==n.fontMatrix[0]&&0!==n.fontMatrix[3]||(0,s.warn)("Invalid font matrix for font "+e);if(t<0){t=-t;n.fontDirection=-1}else n.fontDirection=1;this.current.font=r;this.current.fontSize=t;if(r.isType3Font)return;const a=r.loadedName||"sans-serif";let i="normal";r.black?i="900":r.bold&&(i="bold");const o=r.italic?"italic":"normal",l=`"${a}", ${r.fallbackName}`;let c=t;t<16?c=16:t>100&&(c=100);this.current.fontSizeScale=t/c;this.ctx.font=`${o} ${i} ${c}px ${l}`}setTextRenderingMode(e){this.current.textRenderingMode=e}setTextRise(e){this.current.textRise=e}moveText(e,t){this.current.x=this.current.lineX+=e;this.current.y=this.current.lineY+=t}setLeadingMoveText(e,t){this.setLeading(-t);this.moveText(e,t)}setTextMatrix(e,t,r,s,n,a){this.current.textMatrix=[e,t,r,s,n,a];this.current.textMatrixScale=Math.hypot(e,t);this.current.x=this.current.lineX=0;this.current.y=this.current.lineY=0}nextLine(){this.moveText(0,this.current.leading)}paintChar(e,t,r,n,a){const i=this.ctx,o=this.current,l=o.font,c=o.textRenderingMode,h=o.fontSize/o.fontSizeScale,d=c&s.TextRenderingMode.FILL_STROKE_MASK,u=!!(c&s.TextRenderingMode.ADD_TO_PATH_FLAG),p=o.patternFill&&!l.missingFile;let g;(l.disableFontFace||u||p)&&(g=l.getPathGenerator(this.commonObjs,e));if(l.disableFontFace||p){i.save();i.translate(t,r);i.beginPath();g(i,h);n&&i.setTransform.apply(i,n);d!==s.TextRenderingMode.FILL&&d!==s.TextRenderingMode.FILL_STROKE||i.fill();if(d===s.TextRenderingMode.STROKE||d===s.TextRenderingMode.FILL_STROKE){if(a){i.resetTransform();i.lineWidth=Math.round(this._combinedScaleFactor)}i.stroke()}i.restore()}else{d!==s.TextRenderingMode.FILL&&d!==s.TextRenderingMode.FILL_STROKE||i.fillText(e,t,r);if(d===s.TextRenderingMode.STROKE||d===s.TextRenderingMode.FILL_STROKE)if(a){i.save();i.moveTo(t,r);i.resetTransform();i.lineWidth=Math.round(this._combinedScaleFactor);i.strokeText(e,0,0);i.restore()}else i.strokeText(e,t,r)}if(u){(this.pendingTextPaths||(this.pendingTextPaths=[])).push({transform:i.mozCurrentTransform,x:t,y:r,fontSize:h,addToPath:g})}}get isFontSubpixelAAEnabled(){const{context:e}=this.cachedCanvases.getCanvas("isFontSubpixelAAEnabled",10,10);e.scale(1.5,1);e.fillText("I",0,10);const t=e.getImageData(0,0,10,10).data;let r=!1;for(let e=3;e<t.length;e+=4)if(t[e]>0&&t[e]<255){r=!0;break}return(0,s.shadow)(this,"isFontSubpixelAAEnabled",r)}showText(e){const t=this.current,r=t.font;if(r.isType3Font)return this.showType3Text(e);const n=t.fontSize;if(0===n)return;const a=this.ctx,i=t.fontSizeScale,o=t.charSpacing,l=t.wordSpacing,c=t.fontDirection,h=t.textHScale*c,d=e.length,u=r.vertical,p=u?1:-1,g=r.defaultVMetrics,f=n*t.fontMatrix[0],m=t.textRenderingMode===s.TextRenderingMode.FILL&&!r.disableFontFace&&!t.patternFill;a.save();let A;if(t.patternFill){a.save();const e=t.fillColor.getPattern(a,this,a.mozCurrentTransformInverse);A=a.mozCurrentTransform;a.restore();a.fillStyle=e}a.transform.apply(a,t.textMatrix);a.translate(t.x,t.y+t.textRise);c>0?a.scale(h,-1):a.scale(h,1);let _=t.lineWidth,b=!1;const y=t.textMatrixScale;if(0===y||0===_){const e=t.textRenderingMode&s.TextRenderingMode.FILL_STROKE_MASK;if(e===s.TextRenderingMode.STROKE||e===s.TextRenderingMode.FILL_STROKE){this._cachedGetSinglePixelWidth=null;_=this.getSinglePixelWidth();b=_<0}}else _/=y;if(1!==i){a.scale(i,i);_/=i}a.lineWidth=_;let S,v=0;for(S=0;S<d;++S){const t=e[S];if((0,s.isNum)(t)){v+=p*t*n/1e3;continue}let h=!1;const d=(t.isSpace?l:0)+o,_=t.fontChar,y=t.accent;let x,C,P,k=t.width;if(u){const e=t.vmetric||g,r=-(t.vmetric?e[1]:.5*k)*f,s=e[2]*f;k=e?-e[0]:k;x=r/i;C=(v+s)/i}else{x=v/i;C=0}if(r.remeasure&&k>0){const e=1e3*a.measureText(_).width/n*i;if(k<e&&this.isFontSubpixelAAEnabled){const t=k/e;h=!0;a.save();a.scale(t,1);x/=t}else k!==e&&(x+=(k-e)/2e3*n/i)}if(this.contentVisible&&(t.isInFont||r.missingFile))if(m&&!y)a.fillText(_,x,C);else{this.paintChar(_,x,C,A,b);if(y){const e=x+n*y.offset.x/i,t=C-n*y.offset.y/i;this.paintChar(y.fontChar,e,t,A,b)}}P=u?k*f-d*c:k*f+d*c;v+=P;h&&a.restore()}u?t.y-=v:t.x+=v*h;a.restore()}showType3Text(e){const t=this.ctx,r=this.current,n=r.font,a=r.fontSize,i=r.fontDirection,o=n.vertical?1:-1,l=r.charSpacing,c=r.wordSpacing,h=r.textHScale*i,d=r.fontMatrix||s.FONT_IDENTITY_MATRIX,u=e.length;let p,g,f,m;if(!(r.textRenderingMode===s.TextRenderingMode.INVISIBLE)&&0!==a){this._cachedGetSinglePixelWidth=null;t.save();t.transform.apply(t,r.textMatrix);t.translate(r.x,r.y);t.scale(h,i);for(p=0;p<u;++p){g=e[p];if((0,s.isNum)(g)){m=o*g*a/1e3;this.ctx.translate(m,0);r.x+=m*h;continue}const i=(g.isSpace?c:0)+l,u=n.charProcOperatorList[g.operatorListId];if(!u){(0,s.warn)(`Type3 character "${g.operatorListId}" is not available.`);continue}if(this.contentVisible){this.processingType3=g;this.save();t.scale(a,a);t.transform.apply(t,d);this.executeOperatorList(u);this.restore()}f=s.Util.applyTransform([g.width,0],d)[0]*a+i;t.translate(f,0);r.x+=f*h}t.restore();this.processingType3=null}}setCharWidth(e,t){}setCharWidthAndBounds(e,t,r,s,n,a){this.ctx.rect(r,s,n-r,a-s);this.clip();this.endPath()}getColorN_Pattern(e){let t;if("TilingPattern"===e[0]){const r=e[1],s=this.baseTransform||this.ctx.mozCurrentTransform.slice(),a={createCanvasGraphics:e=>new CanvasGraphics(e,this.commonObjs,this.objs,this.canvasFactory)};t=new n.TilingPattern(e,r,this.ctx,a,s)}else t=this._getPattern(e[1]);return t}setStrokeColorN(){this.current.strokeColor=this.getColorN_Pattern(arguments)}setFillColorN(){this.current.fillColor=this.getColorN_Pattern(arguments);this.current.patternFill=!0}setStrokeRGBColor(e,t,r){const n=s.Util.makeHexColor(e,t,r);this.ctx.strokeStyle=n;this.current.strokeColor=n}setFillRGBColor(e,t,r){const n=s.Util.makeHexColor(e,t,r);this.ctx.fillStyle=n;this.current.fillColor=n;this.current.patternFill=!1}_getPattern(e){if(this.cachedPatterns.has(e))return this.cachedPatterns.get(e);const t=(0,n.getShadingPattern)(this.objs.get(e));this.cachedPatterns.set(e,t);return t}shadingFill(e){if(!this.contentVisible)return;const t=this.ctx;this.save();const r=this._getPattern(e);t.fillStyle=r.getPattern(t,this,t.mozCurrentTransformInverse,!0);const n=t.mozCurrentTransformInverse;if(n){const e=t.canvas,r=e.width,a=e.height,i=s.Util.applyTransform([0,0],n),o=s.Util.applyTransform([0,a],n),l=s.Util.applyTransform([r,0],n),c=s.Util.applyTransform([r,a],n),h=Math.min(i[0],o[0],l[0],c[0]),d=Math.min(i[1],o[1],l[1],c[1]),u=Math.max(i[0],o[0],l[0],c[0]),p=Math.max(i[1],o[1],l[1],c[1]);this.ctx.fillRect(h,d,u-h,p-d)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.restore()}beginInlineImage(){(0,s.unreachable)("Should not call beginInlineImage")}beginImageData(){(0,s.unreachable)("Should not call beginImageData")}paintFormXObjectBegin(e,t){if(this.contentVisible){this.save();this.baseTransformStack.push(this.baseTransform);Array.isArray(e)&&6===e.length&&this.transform.apply(this,e);this.baseTransform=this.ctx.mozCurrentTransform;if(t){const e=t[2]-t[0],r=t[3]-t[1];this.ctx.rect(t[0],t[1],e,r);this.clip();this.endPath()}}}paintFormXObjectEnd(){if(this.contentVisible){this.restore();this.baseTransform=this.baseTransformStack.pop()}}beginGroup(e){if(!this.contentVisible)return;this.save();const t=this.ctx;e.isolated||(0,s.info)("TODO: Support non-isolated groups.");e.knockout&&(0,s.warn)("Knockout groups not supported.");const r=t.mozCurrentTransform;e.matrix&&t.transform.apply(t,e.matrix);if(!e.bbox)throw new Error("Bounding box is required.");let n=s.Util.getAxialAlignedBoundingBox(e.bbox,t.mozCurrentTransform);const i=[0,0,t.canvas.width,t.canvas.height];n=s.Util.intersect(n,i)||[0,0,0,0];const o=Math.floor(n[0]),l=Math.floor(n[1]);let c=Math.max(Math.ceil(n[2])-o,1),h=Math.max(Math.ceil(n[3])-l,1),d=1,u=1;if(c>a){d=c/a;c=a}if(h>a){u=h/a;h=a}let p="groupAt"+this.groupLevel;e.smask&&(p+="_smask_"+this.smaskCounter++%2);const g=this.cachedCanvases.getCanvas(p,c,h,!0),f=g.context;f.scale(1/d,1/u);f.translate(-o,-l);f.transform.apply(f,r);if(e.smask)this.smaskStack.push({canvas:g.canvas,context:f,offsetX:o,offsetY:l,scaleX:d,scaleY:u,subtype:e.smask.subtype,backdrop:e.smask.backdrop,transferMap:e.smask.transferMap||null,startTransformInverse:null});else{t.setTransform(1,0,0,1,0,0);t.translate(o,l);t.scale(d,u)}copyCtxState(t,f);this.ctx=f;this.setGState([["BM","source-over"],["ca",1],["CA",1]]);this.groupStack.push(t);this.groupLevel++;this.current.activeSMask=null}endGroup(e){if(!this.contentVisible)return;this.groupLevel--;const t=this.ctx;this.ctx=this.groupStack.pop();void 0!==this.ctx.imageSmoothingEnabled?this.ctx.imageSmoothingEnabled=!1:this.ctx.mozImageSmoothingEnabled=!1;e.smask?this.tempSMask=this.smaskStack.pop():this.ctx.drawImage(t.canvas,0,0);this.restore()}beginAnnotations(){this.save();this.baseTransform&&this.ctx.setTransform.apply(this.ctx,this.baseTransform)}endAnnotations(){this.restore()}beginAnnotation(e,t,r,s){this.save();resetCtxToDefault(this.ctx);this.current=new CanvasExtraState;if(Array.isArray(t)&&4===t.length){const e=t[2]-t[0],r=t[3]-t[1];this.ctx.rect(t[0],t[1],e,r);this.clip();this.endPath()}this.transform.apply(this,r);this.transform.apply(this,s)}endAnnotation(){this.restore()}paintImageMaskXObject(e){if(!this.contentVisible)return;const t=this.ctx,r=e.width,s=e.height,n=this.processingType3;n&&void 0===n.compiled&&(n.compiled=r<=1e3&&s<=1e3?function compileType3Glyph(e){const t=new Uint8Array([0,2,4,0,1,0,5,4,8,10,0,8,0,2,1,0]),r=e.width,s=e.height,n=r+1;let a,i,o,l;const c=new Uint8Array(n*(s+1)),h=r+7&-8,d=e.data,u=new Uint8Array(h*s);let p=0;for(a=0,i=d.length;a<i;a++){const e=d[a];let t=128;for(;t>0;){u[p++]=e&t?0:255;t>>=1}}let g=0;p=0;if(0!==u[p]){c[0]=1;++g}for(o=1;o<r;o++){if(u[p]!==u[p+1]){c[o]=u[p]?2:1;++g}p++}if(0!==u[p]){c[o]=2;++g}for(a=1;a<s;a++){p=a*h;l=a*n;if(u[p-h]!==u[p]){c[l]=u[p]?1:8;++g}let e=(u[p]?4:0)+(u[p-h]?8:0);for(o=1;o<r;o++){e=(e>>2)+(u[p+1]?4:0)+(u[p-h+1]?8:0);if(t[e]){c[l+o]=t[e];++g}p++}if(u[p-h]!==u[p]){c[l+o]=u[p]?2:4;++g}if(g>1e3)return null}p=h*(s-1);l=a*n;if(0!==u[p]){c[l]=8;++g}for(o=1;o<r;o++){if(u[p]!==u[p+1]){c[l+o]=u[p]?4:8;++g}p++}if(0!==u[p]){c[l+o]=4;++g}if(g>1e3)return null;const f=new Int32Array([0,n,-1,0,-n,0,0,0,1]),m=[];for(a=0;g&&a<=s;a++){let e=a*n;const t=e+r;for(;e<t&&!c[e];)e++;if(e===t)continue;const s=[e%n,a],i=e;let o=c[e];do{const t=f[o];do{e+=t}while(!c[e]);const r=c[e];if(5!==r&&10!==r){o=r;c[e]=0}else{o=r&51*o>>4;c[e]&=o>>2|o<<2}s.push(e%n,e/n|0);c[e]||--g}while(i!==e);m.push(s);--a}return function(e){e.save();e.scale(1/r,-1/s);e.translate(0,-s);e.beginPath();for(let t=0,r=m.length;t<r;t++){const r=m[t];e.moveTo(r[0],r[1]);for(let t=2,s=r.length;t<s;t+=2)e.lineTo(r[t],r[t+1])}e.fill();e.beginPath();e.restore()}}({data:e.data,width:r,height:s}):null);if(n?.compiled){n.compiled(t);return}const a=this._createMaskCanvas(e),i=a.canvas;t.save();t.setTransform(1,0,0,1,0,0);t.drawImage(i,a.offsetX,a.offsetY);t.restore()}paintImageMaskXObjectRepeat(e,t,r=0,n=0,a,i){if(!this.contentVisible)return;const o=this.ctx;o.save();const l=o.mozCurrentTransform;o.transform(t,r,n,a,0,0);const c=this._createMaskCanvas(e);o.setTransform(1,0,0,1,0,0);for(let e=0,h=i.length;e<h;e+=2){const h=s.Util.transform(l,[t,r,n,a,i[e],i[e+1]]),[d,u]=s.Util.applyTransform([0,0],h);o.drawImage(c.canvas,d,u)}o.restore()}paintImageMaskXObjectGroup(e){if(!this.contentVisible)return;const t=this.ctx,r=this.current.fillColor,s=this.current.patternFill;for(let n=0,a=e.length;n<a;n++){const a=e[n],i=a.width,o=a.height,l=this.cachedCanvases.getCanvas("maskCanvas",i,o),c=l.context;c.save();putBinaryImageMask(c,a);c.globalCompositeOperation="source-in";c.fillStyle=s?r.getPattern(c,this,t.mozCurrentTransformInverse,!1):r;c.fillRect(0,0,i,o);c.restore();t.save();t.transform.apply(t,a.transform);t.scale(1,-1);t.drawImage(l.canvas,0,0,i,o,0,-1,1,1);t.restore()}}paintImageXObject(e){if(!this.contentVisible)return;const t=e.startsWith("g_")?this.commonObjs.get(e):this.objs.get(e);t?this.paintInlineImageXObject(t):(0,s.warn)("Dependent image isn't ready yet")}paintImageXObjectRepeat(e,t,r,n){if(!this.contentVisible)return;const a=e.startsWith("g_")?this.commonObjs.get(e):this.objs.get(e);if(!a){(0,s.warn)("Dependent image isn't ready yet");return}const i=a.width,o=a.height,l=[];for(let e=0,s=n.length;e<s;e+=2)l.push({transform:[t,0,0,r,n[e],n[e+1]],x:0,y:0,w:i,h:o});this.paintInlineImageXObjectGroup(a,l)}paintInlineImageXObject(e){if(!this.contentVisible)return;const t=e.width,r=e.height,s=this.ctx;this.save();s.scale(1/t,-1/r);let n;if("function"==typeof HTMLElement&&e instanceof HTMLElement||!e.data)n=e;else{const s=this.cachedCanvases.getCanvas("inlineImage",t,r);putBinaryImageData(s.context,e,this.current.transferMaps);n=s.canvas}const a=this._scaleImage(n,s.mozCurrentTransformInverse);s.drawImage(a.img,0,0,a.paintWidth,a.paintHeight,0,-r,t,r);if(this.imageLayer){const n=this.getCanvasPosition(0,-r);this.imageLayer.appendImage({imgData:e,left:n[0],top:n[1],width:t/s.mozCurrentTransformInverse[0],height:r/s.mozCurrentTransformInverse[3]})}this.restore()}paintInlineImageXObjectGroup(e,t){if(!this.contentVisible)return;const r=this.ctx,s=e.width,n=e.height,a=this.cachedCanvases.getCanvas("inlineImage",s,n);putBinaryImageData(a.context,e,this.current.transferMaps);for(let i=0,o=t.length;i<o;i++){const o=t[i];r.save();r.transform.apply(r,o.transform);r.scale(1,-1);r.drawImage(a.canvas,o.x,o.y,o.w,o.h,0,-1,1,1);if(this.imageLayer){const t=this.getCanvasPosition(o.x,o.y);this.imageLayer.appendImage({imgData:e,left:t[0],top:t[1],width:s,height:n})}r.restore()}}paintSolidColorImageMask(){this.contentVisible&&this.ctx.fillRect(0,0,1,1)}markPoint(e){}markPointProps(e,t){}beginMarkedContent(e){this.markedContentStack.push({visible:!0})}beginMarkedContentProps(e,t){"OC"===e?this.markedContentStack.push({visible:this.optionalContentConfig.isVisible(t)}):this.markedContentStack.push({visible:!0});this.contentVisible=this.isContentVisible()}endMarkedContent(){this.markedContentStack.pop();this.contentVisible=this.isContentVisible()}beginCompat(){}endCompat(){}consumePath(){const e=this.ctx;if(this.pendingClip){this.pendingClip===o?e.clip("evenodd"):e.clip();this.pendingClip=null}e.beginPath()}getSinglePixelWidth(){if(null===this._cachedGetSinglePixelWidth){const e=this.ctx.mozCurrentTransform,t=Math.abs(e[0]*e[3]-e[2]*e[1]),r=e[0]**2+e[2]**2,s=e[1]**2+e[3]**2,n=Math.sqrt(Math.max(r,s))/t;r!==s&&this._combinedScaleFactor*n>1?this._cachedGetSinglePixelWidth=-this._combinedScaleFactor*n:t>Number.EPSILON?this._cachedGetSinglePixelWidth=n:this._cachedGetSinglePixelWidth=1}return this._cachedGetSinglePixelWidth}getCanvasPosition(e,t){const r=this.ctx.mozCurrentTransform;return[r[0]*e+r[2]*t+r[4],r[1]*e+r[3]*t+r[5]]}isContentVisible(){for(let e=this.markedContentStack.length-1;e>=0;e--)if(!this.markedContentStack[e].visible)return!1;return!0}}for(const e in s.OPS)CanvasGraphics.prototype[s.OPS[e]]=CanvasGraphics.prototype[e];return CanvasGraphics}();t.CanvasGraphics=o},(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});t.getShadingPattern=function getShadingPattern(e){switch(e[0]){case"RadialAxial":return new RadialAxialShadingPattern(e);case"Mesh":return new MeshShadingPattern(e);case"Dummy":return new DummyShadingPattern}throw new Error(`Unknown IR type: ${e[0]}`)};t.TilingPattern=void 0;var s=r(2);function applyBoundingBox(e,t){if(!t||"undefined"==typeof Path2D)return;const r=t[2]-t[0],s=t[3]-t[1],n=new Path2D;n.rect(t[0],t[1],r,s);e.clip(n)}class BaseShadingPattern{constructor(){this.constructor===BaseShadingPattern&&(0,s.unreachable)("Cannot initialize BaseShadingPattern.")}getPattern(){(0,s.unreachable)("Abstract method `getPattern` called.")}}class RadialAxialShadingPattern extends BaseShadingPattern{constructor(e){super();this._type=e[1];this._bbox=e[2];this._colorStops=e[3];this._p0=e[4];this._p1=e[5];this._r0=e[6];this._r1=e[7];this._matrix=e[8];this._patternCache=null}_createGradient(e){let t;"axial"===this._type?t=e.createLinearGradient(this._p0[0],this._p0[1],this._p1[0],this._p1[1]):"radial"===this._type&&(t=e.createRadialGradient(this._p0[0],this._p0[1],this._r0,this._p1[0],this._p1[1],this._r1));for(const e of this._colorStops)t.addColorStop(e[0],e[1]);return t}getPattern(e,t,r,n=!1){let a;if(this._patternCache)a=this._patternCache;else{if(n){applyBoundingBox(e,this._bbox);a=this._createGradient(e)}else{const r=t.cachedCanvases.getCanvas("pattern",t.ctx.canvas.width,t.ctx.canvas.height,!0),s=r.context;s.clearRect(0,0,s.canvas.width,s.canvas.height);s.beginPath();s.rect(0,0,s.canvas.width,s.canvas.height);s.setTransform.apply(s,t.baseTransform);this._matrix&&s.transform.apply(s,this._matrix);applyBoundingBox(s,this._bbox);s.fillStyle=this._createGradient(s);s.fill();a=e.createPattern(r.canvas,"repeat")}this._patternCache=a}if(!n){const e=new DOMMatrix(r);try{a.setTransform(e)}catch(e){(0,s.warn)(`RadialAxialShadingPattern.getPattern: "${e?.message}".`)}}return a}}function drawTriangle(e,t,r,s,n,a,i,o){const l=t.coords,c=t.colors,h=e.data,d=4*e.width;let u;if(l[r+1]>l[s+1]){u=r;r=s;s=u;u=a;a=i;i=u}if(l[s+1]>l[n+1]){u=s;s=n;n=u;u=i;i=o;o=u}if(l[r+1]>l[s+1]){u=r;r=s;s=u;u=a;a=i;i=u}const p=(l[r]+t.offsetX)*t.scaleX,g=(l[r+1]+t.offsetY)*t.scaleY,f=(l[s]+t.offsetX)*t.scaleX,m=(l[s+1]+t.offsetY)*t.scaleY,A=(l[n]+t.offsetX)*t.scaleX,_=(l[n+1]+t.offsetY)*t.scaleY;if(g>=_)return;const b=c[a],y=c[a+1],S=c[a+2],v=c[i],x=c[i+1],C=c[i+2],P=c[o],k=c[o+1],w=c[o+2],R=Math.round(g),F=Math.round(_);let T,E,M,D,L,I,O,N;for(let e=R;e<=F;e++){if(e<m){let t;t=e<g?0:(g-e)/(g-m);T=p-(p-f)*t;E=b-(b-v)*t;M=y-(y-x)*t;D=S-(S-C)*t}else{let t;t=e>_?1:m===_?0:(m-e)/(m-_);T=f-(f-A)*t;E=v-(v-P)*t;M=x-(x-k)*t;D=C-(C-w)*t}let t;t=e<g?0:e>_?1:(g-e)/(g-_);L=p-(p-A)*t;I=b-(b-P)*t;O=y-(y-k)*t;N=S-(S-w)*t;const r=Math.round(Math.min(T,L)),s=Math.round(Math.max(T,L));let n=d*e+4*r;for(let e=r;e<=s;e++){t=(T-e)/(T-L);t<0?t=0:t>1&&(t=1);h[n++]=E-(E-I)*t|0;h[n++]=M-(M-O)*t|0;h[n++]=D-(D-N)*t|0;h[n++]=255}}}function drawFigure(e,t,r){const s=t.coords,n=t.colors;let a,i;switch(t.type){case"lattice":const o=t.verticesPerRow,l=Math.floor(s.length/o)-1,c=o-1;for(a=0;a<l;a++){let t=a*o;for(let a=0;a<c;a++,t++){drawTriangle(e,r,s[t],s[t+1],s[t+o],n[t],n[t+1],n[t+o]);drawTriangle(e,r,s[t+o+1],s[t+1],s[t+o],n[t+o+1],n[t+1],n[t+o])}}break;case"triangles":for(a=0,i=s.length;a<i;a+=3)drawTriangle(e,r,s[a],s[a+1],s[a+2],n[a],n[a+1],n[a+2]);break;default:throw new Error("illegal figure")}}class MeshShadingPattern extends BaseShadingPattern{constructor(e){super();this._coords=e[2];this._colors=e[3];this._figures=e[4];this._bounds=e[5];this._matrix=e[6];this._bbox=e[7];this._background=e[8]}_createMeshCanvas(e,t,r){const s=Math.floor(this._bounds[0]),n=Math.floor(this._bounds[1]),a=Math.ceil(this._bounds[2])-s,i=Math.ceil(this._bounds[3])-n,o=Math.min(Math.ceil(Math.abs(a*e[0]*1.1)),3e3),l=Math.min(Math.ceil(Math.abs(i*e[1]*1.1)),3e3),c=a/o,h=i/l,d={coords:this._coords,colors:this._colors,offsetX:-s,offsetY:-n,scaleX:1/c,scaleY:1/h},u=o+4,p=l+4,g=r.getCanvas("mesh",u,p,!1),f=g.context,m=f.createImageData(o,l);if(t){const e=m.data;for(let r=0,s=e.length;r<s;r+=4){e[r]=t[0];e[r+1]=t[1];e[r+2]=t[2];e[r+3]=255}}for(const e of this._figures)drawFigure(m,e,d);f.putImageData(m,2,2);return{canvas:g.canvas,offsetX:s-2*c,offsetY:n-2*h,scaleX:c,scaleY:h}}getPattern(e,t,r,n=!1){applyBoundingBox(e,this._bbox);let a;if(n)a=s.Util.singularValueDecompose2dScale(e.mozCurrentTransform);else{a=s.Util.singularValueDecompose2dScale(t.baseTransform);if(this._matrix){const e=s.Util.singularValueDecompose2dScale(this._matrix);a=[a[0]*e[0],a[1]*e[1]]}}const i=this._createMeshCanvas(a,n?null:this._background,t.cachedCanvases);if(!n){e.setTransform.apply(e,t.baseTransform);this._matrix&&e.transform.apply(e,this._matrix)}e.translate(i.offsetX,i.offsetY);e.scale(i.scaleX,i.scaleY);return e.createPattern(i.canvas,"no-repeat")}}class DummyShadingPattern extends BaseShadingPattern{getPattern(){return"hotpink"}}const n=1,a=2;class TilingPattern{static get MAX_PATTERN_SIZE(){return(0,s.shadow)(this,"MAX_PATTERN_SIZE",3e3)}constructor(e,t,r,s,n){this.operatorList=e[2];this.matrix=e[3]||[1,0,0,1,0,0];this.bbox=e[4];this.xstep=e[5];this.ystep=e[6];this.paintType=e[7];this.tilingType=e[8];this.color=t;this.ctx=r;this.canvasGraphicsFactory=s;this.baseTransform=n}createPatternCanvas(e){const t=this.operatorList,r=this.bbox,n=this.xstep,a=this.ystep,i=this.paintType,o=this.tilingType,l=this.color,c=this.canvasGraphicsFactory;(0,s.info)("TilingType: "+o);const h=r[0],d=r[1],u=r[2],p=r[3],g=s.Util.singularValueDecompose2dScale(this.matrix),f=s.Util.singularValueDecompose2dScale(this.baseTransform),m=[g[0]*f[0],g[1]*f[1]],A=this.getSizeAndScale(n,this.ctx.canvas.width,m[0]),_=this.getSizeAndScale(a,this.ctx.canvas.height,m[1]),b=e.cachedCanvases.getCanvas("pattern",A.size,_.size,!0),y=b.context,S=c.createCanvasGraphics(y);S.groupLevel=e.groupLevel;this.setFillAndStrokeStyleToContext(S,i,l);let v=h,x=d,C=u,P=p;if(h<0){v=0;C+=Math.abs(h)}if(d<0){x=0;P+=Math.abs(d)}y.translate(-A.scale*v,-_.scale*x);S.transform(A.scale,0,0,_.scale,0,0);this.clipBbox(S,v,x,C,P);S.baseTransform=S.ctx.mozCurrentTransform.slice();S.executeOperatorList(t);S.endDrawing();return{canvas:b.canvas,scaleX:A.scale,scaleY:_.scale,offsetX:v,offsetY:x}}getSizeAndScale(e,t,r){e=Math.abs(e);const s=Math.max(TilingPattern.MAX_PATTERN_SIZE,t);let n=Math.ceil(e*r);n>=s?n=s:r=n/e;return{scale:r,size:n}}clipBbox(e,t,r,s,n){const a=s-t,i=n-r;e.ctx.rect(t,r,a,i);e.clip();e.endPath()}setFillAndStrokeStyleToContext(e,t,r){const i=e.ctx,o=e.current;switch(t){case n:const e=this.ctx;i.fillStyle=e.fillStyle;i.strokeStyle=e.strokeStyle;o.fillColor=e.fillStyle;o.strokeColor=e.strokeStyle;break;case a:const l=s.Util.makeHexColor(r[0],r[1],r[2]);i.fillStyle=l;i.strokeStyle=l;o.fillColor=l;o.strokeColor=l;break;default:throw new s.FormatError(`Unsupported paint type: ${t}`)}}getPattern(e,t,r,n=!1){let a=r;if(!n){a=s.Util.transform(a,t.baseTransform);this.matrix&&(a=s.Util.transform(a,this.matrix))}const i=this.createPatternCanvas(t);let o=new DOMMatrix(a);o=o.translate(i.offsetX,i.offsetY);o=o.scale(1/i.scaleX,1/i.scaleY);const l=e.createPattern(i.canvas,"repeat");try{l.setTransform(o)}catch(e){(0,s.warn)(`TilingPattern.getPattern: "${e?.message}".`)}return l}}t.TilingPattern=TilingPattern},(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});t.GlobalWorkerOptions=void 0;const r=Object.create(null);t.GlobalWorkerOptions=r;r.workerPort=void 0===r.workerPort?null:r.workerPort;r.workerSrc=void 0===r.workerSrc?"":r.workerSrc},(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});t.MessageHandler=void 0;var s=r(2);const n=1,a=2,i=1,o=2,l=3,c=4,h=5,d=6,u=7,p=8;function wrapReason(e){if("object"!=typeof e||null===e)return e;switch(e.name){case"AbortException":return new s.AbortException(e.message);case"MissingPDFException":return new s.MissingPDFException(e.message);case"UnexpectedResponseException":return new s.UnexpectedResponseException(e.message,e.status);case"UnknownErrorException":return new s.UnknownErrorException(e.message,e.details);default:return new s.UnknownErrorException(e.message,e.toString())}}t.MessageHandler=class MessageHandler{constructor(e,t,r){this.sourceName=e;this.targetName=t;this.comObj=r;this.callbackId=1;this.streamId=1;this.postMessageTransfers=!0;this.streamSinks=Object.create(null);this.streamControllers=Object.create(null);this.callbackCapabilities=Object.create(null);this.actionHandler=Object.create(null);this._onComObjOnMessage=e=>{const t=e.data;if(t.targetName!==this.sourceName)return;if(t.stream){this._processStreamMessage(t);return}if(t.callback){const e=t.callbackId,r=this.callbackCapabilities[e];if(!r)throw new Error(`Cannot resolve callback ${e}`);delete this.callbackCapabilities[e];if(t.callback===n)r.resolve(t.data);else{if(t.callback!==a)throw new Error("Unexpected callback case");r.reject(wrapReason(t.reason))}return}const s=this.actionHandler[t.action];if(!s)throw new Error(`Unknown action from worker: ${t.action}`);if(t.callbackId){const e=this.sourceName,i=t.sourceName;new Promise((function(e){e(s(t.data))})).then((function(s){r.postMessage({sourceName:e,targetName:i,callback:n,callbackId:t.callbackId,data:s})}),(function(s){r.postMessage({sourceName:e,targetName:i,callback:a,callbackId:t.callbackId,reason:wrapReason(s)})}))}else t.streamId?this._createStreamSink(t):s(t.data)};r.addEventListener("message",this._onComObjOnMessage)}on(e,t){const r=this.actionHandler;if(r[e])throw new Error(`There is already an actionName called "${e}"`);r[e]=t}send(e,t,r){this._postMessage({sourceName:this.sourceName,targetName:this.targetName,action:e,data:t},r)}sendWithPromise(e,t,r){const n=this.callbackId++,a=(0,s.createPromiseCapability)();this.callbackCapabilities[n]=a;try{this._postMessage({sourceName:this.sourceName,targetName:this.targetName,action:e,callbackId:n,data:t},r)}catch(e){a.reject(e)}return a.promise}sendWithStream(e,t,r,n){const a=this.streamId++,o=this.sourceName,l=this.targetName,c=this.comObj;return new ReadableStream({start:r=>{const i=(0,s.createPromiseCapability)();this.streamControllers[a]={controller:r,startCall:i,pullCall:null,cancelCall:null,isClosed:!1};this._postMessage({sourceName:o,targetName:l,action:e,streamId:a,data:t,desiredSize:r.desiredSize},n);return i.promise},pull:e=>{const t=(0,s.createPromiseCapability)();this.streamControllers[a].pullCall=t;c.postMessage({sourceName:o,targetName:l,stream:d,streamId:a,desiredSize:e.desiredSize});return t.promise},cancel:e=>{(0,s.assert)(e instanceof Error,"cancel must have a valid reason");const t=(0,s.createPromiseCapability)();this.streamControllers[a].cancelCall=t;this.streamControllers[a].isClosed=!0;c.postMessage({sourceName:o,targetName:l,stream:i,streamId:a,reason:wrapReason(e)});return t.promise}},r)}_createStreamSink(e){const t=this,r=this.actionHandler[e.action],n=e.streamId,a=this.sourceName,i=e.sourceName,o=this.comObj,d={enqueue(e,r=1,o){if(this.isCancelled)return;const l=this.desiredSize;this.desiredSize-=r;if(l>0&&this.desiredSize<=0){this.sinkCapability=(0,s.createPromiseCapability)();this.ready=this.sinkCapability.promise}t._postMessage({sourceName:a,targetName:i,stream:c,streamId:n,chunk:e},o)},close(){if(!this.isCancelled){this.isCancelled=!0;o.postMessage({sourceName:a,targetName:i,stream:l,streamId:n});delete t.streamSinks[n]}},error(e){(0,s.assert)(e instanceof Error,"error must have a valid reason");if(!this.isCancelled){this.isCancelled=!0;o.postMessage({sourceName:a,targetName:i,stream:h,streamId:n,reason:wrapReason(e)})}},sinkCapability:(0,s.createPromiseCapability)(),onPull:null,onCancel:null,isCancelled:!1,desiredSize:e.desiredSize,ready:null};d.sinkCapability.resolve();d.ready=d.sinkCapability.promise;this.streamSinks[n]=d;new Promise((function(t){t(r(e.data,d))})).then((function(){o.postMessage({sourceName:a,targetName:i,stream:p,streamId:n,success:!0})}),(function(e){o.postMessage({sourceName:a,targetName:i,stream:p,streamId:n,reason:wrapReason(e)})}))}_processStreamMessage(e){const t=e.streamId,r=this.sourceName,n=e.sourceName,a=this.comObj;switch(e.stream){case p:e.success?this.streamControllers[t].startCall.resolve():this.streamControllers[t].startCall.reject(wrapReason(e.reason));break;case u:e.success?this.streamControllers[t].pullCall.resolve():this.streamControllers[t].pullCall.reject(wrapReason(e.reason));break;case d:if(!this.streamSinks[t]){a.postMessage({sourceName:r,targetName:n,stream:u,streamId:t,success:!0});break}this.streamSinks[t].desiredSize<=0&&e.desiredSize>0&&this.streamSinks[t].sinkCapability.resolve();this.streamSinks[t].desiredSize=e.desiredSize;const{onPull:g}=this.streamSinks[e.streamId];new Promise((function(e){e(g&&g())})).then((function(){a.postMessage({sourceName:r,targetName:n,stream:u,streamId:t,success:!0})}),(function(e){a.postMessage({sourceName:r,targetName:n,stream:u,streamId:t,reason:wrapReason(e)})}));break;case c:(0,s.assert)(this.streamControllers[t],"enqueue should have stream controller");if(this.streamControllers[t].isClosed)break;this.streamControllers[t].controller.enqueue(e.chunk);break;case l:(0,s.assert)(this.streamControllers[t],"close should have stream controller");if(this.streamControllers[t].isClosed)break;this.streamControllers[t].isClosed=!0;this.streamControllers[t].controller.close();this._deleteStreamController(t);break;case h:(0,s.assert)(this.streamControllers[t],"error should have stream controller");this.streamControllers[t].controller.error(wrapReason(e.reason));this._deleteStreamController(t);break;case o:e.success?this.streamControllers[t].cancelCall.resolve():this.streamControllers[t].cancelCall.reject(wrapReason(e.reason));this._deleteStreamController(t);break;case i:if(!this.streamSinks[t])break;const{onCancel:f}=this.streamSinks[e.streamId];new Promise((function(t){t(f&&f(wrapReason(e.reason)))})).then((function(){a.postMessage({sourceName:r,targetName:n,stream:o,streamId:t,success:!0})}),(function(e){a.postMessage({sourceName:r,targetName:n,stream:o,streamId:t,reason:wrapReason(e)})}));this.streamSinks[t].sinkCapability.reject(wrapReason(e.reason));this.streamSinks[t].isCancelled=!0;delete this.streamSinks[t];break;default:throw new Error("Unexpected stream case")}}async _deleteStreamController(e){await Promise.allSettled([this.streamControllers[e].startCall,this.streamControllers[e].pullCall,this.streamControllers[e].cancelCall].map((function(e){return e&&e.promise})));delete this.streamControllers[e]}_postMessage(e,t){t&&this.postMessageTransfers?this.comObj.postMessage(e,t):this.comObj.postMessage(e)}destroy(){this.comObj.removeEventListener("message",this._onComObjOnMessage)}}},(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});t.Metadata=void 0;var s=r(2);t.Metadata=class Metadata{constructor({parsedData:e,rawData:t}){this._metadataMap=e;this._data=t}getRaw(){return this._data}get(e){return this._metadataMap.get(e)??null}getAll(){return(0,s.objectFromMap)(this._metadataMap)}has(e){return this._metadataMap.has(e)}}},(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});t.OptionalContentConfig=void 0;var s=r(2);class OptionalContentGroup{constructor(e,t){this.visible=!0;this.name=e;this.intent=t}}t.OptionalContentConfig=class OptionalContentConfig{constructor(e){this.name=null;this.creator=null;this._order=null;this._groups=new Map;if(null!==e){this.name=e.name;this.creator=e.creator;this._order=e.order;for(const t of e.groups)this._groups.set(t.id,new OptionalContentGroup(t.name,t.intent));if("OFF"===e.baseState)for(const e of this._groups)e.visible=!1;for(const t of e.on)this._groups.get(t).visible=!0;for(const t of e.off)this._groups.get(t).visible=!1}}_evaluateVisibilityExpression(e){const t=e.length;if(t<2)return!0;const r=e[0];for(let n=1;n<t;n++){const t=e[n];let a;if(Array.isArray(t))a=this._evaluateVisibilityExpression(t);else{if(!this._groups.has(t)){(0,s.warn)(`Optional content group not found: ${t}`);return!0}a=this._groups.get(t).visible}switch(r){case"And":if(!a)return!1;break;case"Or":if(a)return!0;break;case"Not":return!a;default:return!0}}return"And"===r}isVisible(e){if("OCG"===e.type){if(!this._groups.has(e.id)){(0,s.warn)(`Optional content group not found: ${e.id}`);return!0}return this._groups.get(e.id).visible}if("OCMD"===e.type){if(e.expression)return this._evaluateVisibilityExpression(e.expression);if(!e.policy||"AnyOn"===e.policy){for(const t of e.ids){if(!this._groups.has(t)){(0,s.warn)(`Optional content group not found: ${t}`);return!0}if(this._groups.get(t).visible)return!0}return!1}if("AllOn"===e.policy){for(const t of e.ids){if(!this._groups.has(t)){(0,s.warn)(`Optional content group not found: ${t}`);return!0}if(!this._groups.get(t).visible)return!1}return!0}if("AnyOff"===e.policy){for(const t of e.ids){if(!this._groups.has(t)){(0,s.warn)(`Optional content group not found: ${t}`);return!0}if(!this._groups.get(t).visible)return!0}return!1}if("AllOff"===e.policy){for(const t of e.ids){if(!this._groups.has(t)){(0,s.warn)(`Optional content group not found: ${t}`);return!0}if(this._groups.get(t).visible)return!1}return!0}(0,s.warn)(`Unknown optional content policy ${e.policy}.`);return!0}(0,s.warn)(`Unknown group type ${e.type}.`);return!0}setVisibility(e,t=!0){this._groups.has(e)?this._groups.get(e).visible=!!t:(0,s.warn)(`Optional content group not found: ${e}`)}getOrder(){return this._groups.size?this._order?this._order.slice():Array.from(this._groups.keys()):null}getGroups(){return this._groups.size>0?(0,s.objectFromMap)(this._groups):null}getGroup(e){return this._groups.get(e)||null}}},(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});t.PDFDataTransportStream=void 0;var s=r(2),n=r(1);t.PDFDataTransportStream=class PDFDataTransportStream{constructor(e,t){(0,s.assert)(t,'PDFDataTransportStream - missing required "pdfDataRangeTransport" argument.');this._queuedChunks=[];this._progressiveDone=e.progressiveDone||!1;this._contentDispositionFilename=e.contentDispositionFilename||null;const r=e.initialData;if(r?.length>0){const e=new Uint8Array(r).buffer;this._queuedChunks.push(e)}this._pdfDataRangeTransport=t;this._isStreamingSupported=!e.disableStream;this._isRangeSupported=!e.disableRange;this._contentLength=e.length;this._fullRequestReader=null;this._rangeReaders=[];this._pdfDataRangeTransport.addRangeListener(((e,t)=>{this._onReceiveData({begin:e,chunk:t})}));this._pdfDataRangeTransport.addProgressListener(((e,t)=>{this._onProgress({loaded:e,total:t})}));this._pdfDataRangeTransport.addProgressiveReadListener((e=>{this._onReceiveData({chunk:e})}));this._pdfDataRangeTransport.addProgressiveDoneListener((()=>{this._onProgressiveDone()}));this._pdfDataRangeTransport.transportReady()}_onReceiveData(e){const t=new Uint8Array(e.chunk).buffer;if(void 0===e.begin)this._fullRequestReader?this._fullRequestReader._enqueue(t):this._queuedChunks.push(t);else{const r=this._rangeReaders.some((function(r){if(r._begin!==e.begin)return!1;r._enqueue(t);return!0}));(0,s.assert)(r,"_onReceiveData - no `PDFDataTransportStreamRangeReader` instance found.")}}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}_onProgress(e){if(void 0===e.total){const t=this._rangeReaders[0];t?.onProgress&&t.onProgress({loaded:e.loaded})}else{const t=this._fullRequestReader;t?.onProgress&&t.onProgress({loaded:e.loaded,total:e.total})}}_onProgressiveDone(){this._fullRequestReader&&this._fullRequestReader.progressiveDone();this._progressiveDone=!0}_removeRangeReader(e){const t=this._rangeReaders.indexOf(e);t>=0&&this._rangeReaders.splice(t,1)}getFullReader(){(0,s.assert)(!this._fullRequestReader,"PDFDataTransportStream.getFullReader can only be called once.");const e=this._queuedChunks;this._queuedChunks=null;return new PDFDataTransportStreamReader(this,e,this._progressiveDone,this._contentDispositionFilename)}getRangeReader(e,t){if(t<=this._progressiveDataLength)return null;const r=new PDFDataTransportStreamRangeReader(this,e,t);this._pdfDataRangeTransport.requestDataRange(e,t);this._rangeReaders.push(r);return r}cancelAllRequests(e){this._fullRequestReader&&this._fullRequestReader.cancel(e);for(const t of this._rangeReaders.slice(0))t.cancel(e);this._pdfDataRangeTransport.abort()}};class PDFDataTransportStreamReader{constructor(e,t,r=!1,s=null){this._stream=e;this._done=r||!1;this._filename=(0,n.isPdfFile)(s)?s:null;this._queuedChunks=t||[];this._loaded=0;for(const e of this._queuedChunks)this._loaded+=e.byteLength;this._requests=[];this._headersReady=Promise.resolve();e._fullRequestReader=this;this.onProgress=null}_enqueue(e){if(!this._done){if(this._requests.length>0){this._requests.shift().resolve({value:e,done:!1})}else this._queuedChunks.push(e);this._loaded+=e.byteLength}}get headersReady(){return this._headersReady}get filename(){return this._filename}get isRangeSupported(){return this._stream._isRangeSupported}get isStreamingSupported(){return this._stream._isStreamingSupported}get contentLength(){return this._stream._contentLength}async read(){if(this._queuedChunks.length>0){return{value:this._queuedChunks.shift(),done:!1}}if(this._done)return{value:void 0,done:!0};const e=(0,s.createPromiseCapability)();this._requests.push(e);return e.promise}cancel(e){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0}progressiveDone(){this._done||(this._done=!0)}}class PDFDataTransportStreamRangeReader{constructor(e,t,r){this._stream=e;this._begin=t;this._end=r;this._queuedChunk=null;this._requests=[];this._done=!1;this.onProgress=null}_enqueue(e){if(!this._done){if(0===this._requests.length)this._queuedChunk=e;else{this._requests.shift().resolve({value:e,done:!1});for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0}this._done=!0;this._stream._removeRangeReader(this)}}get isStreamingSupported(){return!1}async read(){if(this._queuedChunk){const e=this._queuedChunk;this._queuedChunk=null;return{value:e,done:!1}}if(this._done)return{value:void 0,done:!0};const e=(0,s.createPromiseCapability)();this._requests.push(e);return e.promise}cancel(e){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0;this._stream._removeRangeReader(this)}}},(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});t.AnnotationLayer=void 0;var s=r(1),n=r(2),a=r(9),i=r(18);class AnnotationElementFactory{static create(e){switch(e.data.annotationType){case n.AnnotationType.LINK:return new LinkAnnotationElement(e);case n.AnnotationType.TEXT:return new TextAnnotationElement(e);case n.AnnotationType.WIDGET:switch(e.data.fieldType){case"Tx":return new TextWidgetAnnotationElement(e);case"Btn":return e.data.radioButton?new RadioButtonWidgetAnnotationElement(e):e.data.checkBox?new CheckboxWidgetAnnotationElement(e):new PushButtonWidgetAnnotationElement(e);case"Ch":return new ChoiceWidgetAnnotationElement(e)}return new WidgetAnnotationElement(e);case n.AnnotationType.POPUP:return new PopupAnnotationElement(e);case n.AnnotationType.FREETEXT:return new FreeTextAnnotationElement(e);case n.AnnotationType.LINE:return new LineAnnotationElement(e);case n.AnnotationType.SQUARE:return new SquareAnnotationElement(e);case n.AnnotationType.CIRCLE:return new CircleAnnotationElement(e);case n.AnnotationType.POLYLINE:return new PolylineAnnotationElement(e);case n.AnnotationType.CARET:return new CaretAnnotationElement(e);case n.AnnotationType.INK:return new InkAnnotationElement(e);case n.AnnotationType.POLYGON:return new PolygonAnnotationElement(e);case n.AnnotationType.HIGHLIGHT:return new HighlightAnnotationElement(e);case n.AnnotationType.UNDERLINE:return new UnderlineAnnotationElement(e);case n.AnnotationType.SQUIGGLY:return new SquigglyAnnotationElement(e);case n.AnnotationType.STRIKEOUT:return new StrikeOutAnnotationElement(e);case n.AnnotationType.STAMP:return new StampAnnotationElement(e);case n.AnnotationType.FILEATTACHMENT:return new FileAttachmentAnnotationElement(e);default:return new AnnotationElement(e)}}}class AnnotationElement{constructor(e,{isRenderable:t=!1,ignoreBorder:r=!1,createQuadrilaterals:s=!1}={}){this.isRenderable=t;this.data=e.data;this.layer=e.layer;this.page=e.page;this.viewport=e.viewport;this.linkService=e.linkService;this.downloadManager=e.downloadManager;this.imageResourcesPath=e.imageResourcesPath;this.renderInteractiveForms=e.renderInteractiveForms;this.svgFactory=e.svgFactory;this.annotationStorage=e.annotationStorage;this.enableScripting=e.enableScripting;this.hasJSActions=e.hasJSActions;this._mouseState=e.mouseState;t&&(this.container=this._createContainer(r));s&&(this.quadrilaterals=this._createQuadrilaterals(r))}_createContainer(e=!1){const t=this.data,r=this.page,s=this.viewport,a=document.createElement("section");let i=t.rect[2]-t.rect[0],o=t.rect[3]-t.rect[1];a.setAttribute("data-annotation-id",t.id);const l=n.Util.normalizeRect([t.rect[0],r.view[3]-t.rect[1]+r.view[1],t.rect[2],r.view[3]-t.rect[3]+r.view[1]]);a.style.transform=`matrix(${s.transform.join(",")})`;a.style.transformOrigin=`${-l[0]}px ${-l[1]}px`;if(!e&&t.borderStyle.width>0){a.style.borderWidth=`${t.borderStyle.width}px`;if(t.borderStyle.style!==n.AnnotationBorderStyleType.UNDERLINE){i-=2*t.borderStyle.width;o-=2*t.borderStyle.width}const e=t.borderStyle.horizontalCornerRadius,r=t.borderStyle.verticalCornerRadius;if(e>0||r>0){const t=`${e}px / ${r}px`;a.style.borderRadius=t}switch(t.borderStyle.style){case n.AnnotationBorderStyleType.SOLID:a.style.borderStyle="solid";break;case n.AnnotationBorderStyleType.DASHED:a.style.borderStyle="dashed";break;case n.AnnotationBorderStyleType.BEVELED:(0,n.warn)("Unimplemented border style: beveled");break;case n.AnnotationBorderStyleType.INSET:(0,n.warn)("Unimplemented border style: inset");break;case n.AnnotationBorderStyleType.UNDERLINE:a.style.borderBottomStyle="solid"}t.color?a.style.borderColor=n.Util.makeHexColor(0|t.color[0],0|t.color[1],0|t.color[2]):a.style.borderWidth=0}a.style.left=`${l[0]}px`;a.style.top=`${l[1]}px`;a.style.width=`${i}px`;a.style.height=`${o}px`;return a}_createQuadrilaterals(e=!1){if(!this.data.quadPoints)return null;const t=[],r=this.data.rect;for(const r of this.data.quadPoints){this.data.rect=[r[2].x,r[2].y,r[1].x,r[1].y];t.push(this._createContainer(e))}this.data.rect=r;return t}_createPopup(e,t){let r=this.container;if(this.quadrilaterals){e=e||this.quadrilaterals;r=this.quadrilaterals[0]}if(!e){(e=document.createElement("div")).style.height=r.style.height;e.style.width=r.style.width;r.appendChild(e)}const s=new PopupElement({container:r,trigger:e,color:t.color,title:t.title,modificationDate:t.modificationDate,contents:t.contents,hideWrapper:!0}).render();s.style.left=r.style.width;r.appendChild(s)}_renderQuadrilaterals(e){for(const t of this.quadrilaterals)t.className=e;return this.quadrilaterals}render(){(0,n.unreachable)("Abstract method `AnnotationElement.render` called")}}class LinkAnnotationElement extends AnnotationElement{constructor(e){super(e,{isRenderable:!!(e.data.url||e.data.dest||e.data.action||e.data.isTooltipOnly||e.data.actions&&(e.data.actions.Action||e.data.actions["Mouse Up"]||e.data.actions["Mouse Down"])),createQuadrilaterals:!0})}render(){const{data:e,linkService:t}=this,r=document.createElement("a");e.url?(0,s.addLinkAttributes)(r,{url:e.url,target:e.newWindow?s.LinkTarget.BLANK:t.externalLinkTarget,rel:t.externalLinkRel,enabled:t.externalLinkEnabled}):e.action?this._bindNamedAction(r,e.action):e.dest?this._bindLink(r,e.dest):e.actions&&(e.actions.Action||e.actions["Mouse Up"]||e.actions["Mouse Down"])&&this.enableScripting&&this.hasJSActions?this._bindJSAction(r,e):this._bindLink(r,"");if(this.quadrilaterals)return this._renderQuadrilaterals("linkAnnotation").map(((e,t)=>{const s=0===t?r:r.cloneNode();e.appendChild(s);return e}));this.container.className="linkAnnotation";this.container.appendChild(r);return this.container}_bindLink(e,t){e.href=this.linkService.getDestinationHash(t);e.onclick=()=>{t&&this.linkService.goToDestination(t);return!1};(t||""===t)&&(e.className="internalLink")}_bindNamedAction(e,t){e.href=this.linkService.getAnchorUrl("");e.onclick=()=>{this.linkService.executeNamedAction(t);return!1};e.className="internalLink"}_bindJSAction(e,t){e.href=this.linkService.getAnchorUrl("");const r=new Map([["Action","onclick"],["Mouse Up","onmouseup"],["Mouse Down","onmousedown"]]);for(const s of Object.keys(t.actions)){const n=r.get(s);n&&(e[n]=()=>{this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:t.id,name:s}});return!1})}e.className="internalLink"}}class TextAnnotationElement extends AnnotationElement{constructor(e){super(e,{isRenderable:!!(e.data.hasPopup||e.data.title||e.data.contents)})}render(){this.container.className="textAnnotation";const e=document.createElement("img");e.style.height=this.container.style.height;e.style.width=this.container.style.width;e.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg";e.alt="[{{type}} Annotation]";e.dataset.l10nId="text_annotation_type";e.dataset.l10nArgs=JSON.stringify({type:this.data.name});this.data.hasPopup||this._createPopup(e,this.data);this.container.appendChild(e);return this.container}}class WidgetAnnotationElement extends AnnotationElement{render(){this.data.alternativeText&&(this.container.title=this.data.alternativeText);return this.container}_getKeyModifier(e){return navigator.platform.includes("Win")&&e.ctrlKey||navigator.platform.includes("Mac")&&e.metaKey}_setEventListener(e,t,r,s){t.includes("mouse")?e.addEventListener(t,(e=>{this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:r,value:s(e),shift:e.shiftKey,modifier:this._getKeyModifier(e)}})})):e.addEventListener(t,(e=>{this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:r,value:e.target.checked}})}))}_setEventListeners(e,t,r){for(const[s,n]of t)("Action"===n||this.data.actions?.[n])&&this._setEventListener(e,s,n,r)}_dispatchEventFromSandbox(e,t){const setColor=(e,t,r)=>{const s=r.detail[e];r.target.style[t]=i.ColorConverters[`${s[0]}_HTML`](s.slice(1))},r={display:e=>{const t=e.detail.display%2==1;e.target.style.visibility=t?"hidden":"visible";this.annotationStorage.setValue(this.data.id,{hidden:t,print:0===e.detail.display||3===e.detail.display})},print:e=>{this.annotationStorage.setValue(this.data.id,{print:e.detail.print})},hidden:e=>{e.target.style.visibility=e.detail.hidden?"hidden":"visible";this.annotationStorage.setValue(this.data.id,{hidden:e.detail.hidden})},focus:e=>{setTimeout((()=>e.target.focus({preventScroll:!1})),0)},userName:e=>{e.target.title=e.detail.userName},readonly:e=>{e.detail.readonly?e.target.setAttribute("readonly",""):e.target.removeAttribute("readonly")},required:e=>{e.detail.required?e.target.setAttribute("required",""):e.target.removeAttribute("required")},bgColor:e=>{setColor("bgColor","backgroundColor",e)},fillColor:e=>{setColor("fillColor","backgroundColor",e)},fgColor:e=>{setColor("fgColor","color",e)},textColor:e=>{setColor("textColor","color",e)},borderColor:e=>{setColor("borderColor","borderColor",e)},strokeColor:e=>{setColor("strokeColor","borderColor",e)}};for(const s of Object.keys(t.detail)){const n=e[s]||r[s];n&&n(t)}}}class TextWidgetAnnotationElement extends WidgetAnnotationElement{constructor(e){super(e,{isRenderable:e.renderInteractiveForms||!e.data.hasAppearance&&!!e.data.fieldValue})}setPropertyOnSiblings(e,t,r,s){const n=this.annotationStorage;for(const a of document.getElementsByName(e.name))if(a!==e){a[t]=r;const e=Object.create(null);e[s]=r;n.setValue(a.getAttribute("id"),e)}}render(){const e=this.annotationStorage,t=this.data.id;this.container.className="textWidgetAnnotation";let r=null;if(this.renderInteractiveForms){const s=e.getValue(t,{value:this.data.fieldValue,valueAsString:this.data.fieldValue}),n=s.valueAsString||s.value||"",a={userValue:null,formattedValue:null,beforeInputSelectionRange:null,beforeInputValue:null};if(this.data.multiLine){r=document.createElement("textarea");r.textContent=n}else{r=document.createElement("input");r.type="text";r.setAttribute("value",n)}a.userValue=n;r.setAttribute("id",t);r.addEventListener("input",(s=>{e.setValue(t,{value:s.target.value});this.setPropertyOnSiblings(r,"value",s.target.value,"value")}));let blurListener=e=>{a.formattedValue&&(e.target.value=a.formattedValue);e.target.scrollLeft=0;a.beforeInputSelectionRange=null};if(this.enableScripting&&this.hasJSActions){r.addEventListener("focus",(e=>{a.userValue&&(e.target.value=a.userValue)}));r.addEventListener("updatefromsandbox",(r=>{const s={value(r){a.userValue=r.detail.value||"";e.setValue(t,{value:a.userValue.toString()});a.formattedValue||(r.target.value=a.userValue)},valueAsString(r){a.formattedValue=r.detail.valueAsString||"";r.target!==document.activeElement&&(r.target.value=a.formattedValue);e.setValue(t,{formattedValue:a.formattedValue})},selRange(e){const[t,r]=e.detail.selRange;t>=0&&r<e.target.value.length&&e.target.setSelectionRange(t,r)}};this._dispatchEventFromSandbox(s,r)}));r.addEventListener("keydown",(e=>{a.beforeInputValue=e.target.value;let r=-1;"Escape"===e.key?r=0:"Enter"===e.key?r=2:"Tab"===e.key&&(r=3);if(-1!==r){a.userValue=e.target.value;this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:t,name:"Keystroke",value:e.target.value,willCommit:!0,commitKey:r,selStart:e.target.selectionStart,selEnd:e.target.selectionEnd}})}}));const s=blurListener;blurListener=null;r.addEventListener("blur",(e=>{if(this._mouseState.isDown){a.userValue=e.target.value;this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:t,name:"Keystroke",value:e.target.value,willCommit:!0,commitKey:1,selStart:e.target.selectionStart,selEnd:e.target.selectionEnd}})}s(e)}));r.addEventListener("mousedown",(e=>{a.beforeInputValue=e.target.value;a.beforeInputSelectionRange=null}));r.addEventListener("keyup",(e=>{e.target.selectionStart===e.target.selectionEnd&&(a.beforeInputSelectionRange=null)}));r.addEventListener("select",(e=>{a.beforeInputSelectionRange=[e.target.selectionStart,e.target.selectionEnd]}));this.data.actions?.Keystroke&&r.addEventListener("input",(e=>{let r=-1,s=-1;a.beforeInputSelectionRange&&([r,s]=a.beforeInputSelectionRange);this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:t,name:"Keystroke",value:a.beforeInputValue,change:e.data,willCommit:!1,selStart:r,selEnd:s}})}));this._setEventListeners(r,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(e=>e.target.value))}blurListener&&r.addEventListener("blur",blurListener);r.disabled=this.data.readOnly;r.name=this.data.fieldName;null!==this.data.maxLen&&(r.maxLength=this.data.maxLen);if(this.data.comb){const e=(this.data.rect[2]-this.data.rect[0])/this.data.maxLen;r.classList.add("comb");r.style.letterSpacing=`calc(${e}px - 1ch)`}}else{r=document.createElement("div");r.textContent=this.data.fieldValue;r.style.verticalAlign="middle";r.style.display="table-cell"}this._setTextStyle(r);this.container.appendChild(r);return this.container}_setTextStyle(e){const t=["left","center","right"],{fontSize:r,fontColor:s}=this.data.defaultAppearanceData,a=e.style;r&&(a.fontSize=`${r}px`);a.color=n.Util.makeHexColor(s[0],s[1],s[2]);null!==this.data.textAlignment&&(a.textAlign=t[this.data.textAlignment])}}class CheckboxWidgetAnnotationElement extends WidgetAnnotationElement{constructor(e){super(e,{isRenderable:e.renderInteractiveForms})}render(){const e=this.annotationStorage,t=this.data,r=t.id;let s=e.getValue(r,{value:t.fieldValue&&(t.exportValue&&t.exportValue===t.fieldValue||!t.exportValue&&"Off"!==t.fieldValue)}).value;if("string"==typeof s){s="Off"!==s;e.setValue(r,{value:s})}this.container.className="buttonWidgetAnnotation checkBox";const n=document.createElement("input");n.disabled=t.readOnly;n.type="checkbox";n.name=this.data.fieldName;s&&n.setAttribute("checked",!0);n.setAttribute("id",r);n.addEventListener("change",(function(t){const s=t.target.name;for(const r of document.getElementsByName(s))if(r!==t.target){r.checked=!1;e.setValue(r.parentNode.getAttribute("data-annotation-id"),{value:!1})}e.setValue(r,{value:t.target.checked})}));if(this.enableScripting&&this.hasJSActions){n.addEventListener("updatefromsandbox",(t=>{const s={value(t){t.target.checked="Off"!==t.detail.value;e.setValue(r,{value:t.target.checked})}};this._dispatchEventFromSandbox(s,t)}));this._setEventListeners(n,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(e=>e.target.checked))}this.container.appendChild(n);return this.container}}class RadioButtonWidgetAnnotationElement extends WidgetAnnotationElement{constructor(e){super(e,{isRenderable:e.renderInteractiveForms})}render(){this.container.className="buttonWidgetAnnotation radioButton";const e=this.annotationStorage,t=this.data,r=t.id;let s=e.getValue(r,{value:t.fieldValue===t.buttonValue}).value;if("string"==typeof s){s=s!==t.buttonValue;e.setValue(r,{value:s})}const n=document.createElement("input");n.disabled=t.readOnly;n.type="radio";n.name=t.fieldName;s&&n.setAttribute("checked",!0);n.setAttribute("id",r);n.addEventListener("change",(function(t){const{target:s}=t;for(const t of document.getElementsByName(s.name))t!==s&&e.setValue(t.getAttribute("id"),{value:!1});e.setValue(r,{value:s.checked})}));if(this.enableScripting&&this.hasJSActions){const s=t.buttonValue;n.addEventListener("updatefromsandbox",(t=>{const n={value(t){const n=s===t.detail.value;for(const s of document.getElementsByName(t.target.name)){const t=s.getAttribute("id");s.checked=t===r&&n;e.setValue(t,{value:s.checked})}}};this._dispatchEventFromSandbox(n,t)}));this._setEventListeners(n,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(e=>e.target.checked))}this.container.appendChild(n);return this.container}}class PushButtonWidgetAnnotationElement extends LinkAnnotationElement{render(){const e=super.render();e.className="buttonWidgetAnnotation pushButton";this.data.alternativeText&&(e.title=this.data.alternativeText);return e}}class ChoiceWidgetAnnotationElement extends WidgetAnnotationElement{constructor(e){super(e,{isRenderable:e.renderInteractiveForms})}render(){this.container.className="choiceWidgetAnnotation";const e=this.annotationStorage,t=this.data.id;e.getValue(t,{value:this.data.fieldValue.length>0?this.data.fieldValue[0]:void 0});const r=document.createElement("select");r.disabled=this.data.readOnly;r.name=this.data.fieldName;r.setAttribute("id",t);if(!this.data.combo){r.size=this.data.options.length;this.data.multiSelect&&(r.multiple=!0)}for(const e of this.data.options){const t=document.createElement("option");t.textContent=e.displayValue;t.value=e.exportValue;this.data.fieldValue.includes(e.exportValue)&&t.setAttribute("selected",!0);r.appendChild(t)}const getValue=(e,t)=>{const r=t?"value":"textContent",s=e.target.options;return e.target.multiple?Array.prototype.filter.call(s,(e=>e.selected)).map((e=>e[r])):-1===s.selectedIndex?null:s[s.selectedIndex][r]},getItems=e=>{const t=e.target.options;return Array.prototype.map.call(t,(e=>({displayValue:e.textContent,exportValue:e.value})))};if(this.enableScripting&&this.hasJSActions){r.addEventListener("updatefromsandbox",(s=>{const n={value(s){const n=r.options,a=s.detail.value,i=new Set(Array.isArray(a)?a:[a]);Array.prototype.forEach.call(n,(e=>{e.selected=i.has(e.value)}));e.setValue(t,{value:getValue(s,!0)})},multipleSelection(e){r.multiple=!0},remove(s){const n=r.options,a=s.detail.remove;n[a].selected=!1;r.remove(a);if(n.length>0){-1===Array.prototype.findIndex.call(n,(e=>e.selected))&&(n[0].selected=!0)}e.setValue(t,{value:getValue(s,!0),items:getItems(s)})},clear(s){for(;0!==r.length;)r.remove(0);e.setValue(t,{value:null,items:[]})},insert(s){const{index:n,displayValue:a,exportValue:i}=s.detail.insert,o=document.createElement("option");o.textContent=a;o.value=i;r.insertBefore(o,r.children[n]);e.setValue(t,{value:getValue(s,!0),items:getItems(s)})},items(s){const{items:n}=s.detail;for(;0!==r.length;)r.remove(0);for(const e of n){const{displayValue:t,exportValue:s}=e,n=document.createElement("option");n.textContent=t;n.value=s;r.appendChild(n)}r.options.length>0&&(r.options[0].selected=!0);e.setValue(t,{value:getValue(s,!0),items:getItems(s)})},indices(r){const s=new Set(r.detail.indices),n=r.target.options;Array.prototype.forEach.call(n,((e,t)=>{e.selected=s.has(t)}));e.setValue(t,{value:getValue(r,!0)})},editable(e){e.target.disabled=!e.detail.editable}};this._dispatchEventFromSandbox(n,s)}));r.addEventListener("input",(r=>{const s=getValue(r,!0),n=getValue(r,!1);e.setValue(t,{value:s});this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:t,name:"Keystroke",value:n,changeEx:s,willCommit:!0,commitKey:1,keyDown:!1}})}));this._setEventListeners(r,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"],["input","Action"]],(e=>e.target.checked))}else r.addEventListener("input",(function(r){e.setValue(t,{value:getValue(r)})}));this.container.appendChild(r);return this.container}}class PopupAnnotationElement extends AnnotationElement{constructor(e){super(e,{isRenderable:!(!e.data.title&&!e.data.contents)})}render(){this.container.className="popupAnnotation";if(["Line","Square","Circle","PolyLine","Polygon","Ink"].includes(this.data.parentType))return this.container;const e=`[data-annotation-id="${this.data.parentId}"]`,t=this.layer.querySelectorAll(e);if(0===t.length)return this.container;const r=new PopupElement({container:this.container,trigger:Array.from(t),color:this.data.color,title:this.data.title,modificationDate:this.data.modificationDate,contents:this.data.contents}),s=this.page,a=n.Util.normalizeRect([this.data.parentRect[0],s.view[3]-this.data.parentRect[1]+s.view[1],this.data.parentRect[2],s.view[3]-this.data.parentRect[3]+s.view[1]]),i=a[0]+this.data.parentRect[2]-this.data.parentRect[0],o=a[1];this.container.style.transformOrigin=`${-i}px ${-o}px`;this.container.style.left=`${i}px`;this.container.style.top=`${o}px`;this.container.appendChild(r.render());return this.container}}class PopupElement{constructor(e){this.container=e.container;this.trigger=e.trigger;this.color=e.color;this.title=e.title;this.modificationDate=e.modificationDate;this.contents=e.contents;this.hideWrapper=e.hideWrapper||!1;this.pinned=!1}render(){const e=document.createElement("div");e.className="popupWrapper";this.hideElement=this.hideWrapper?e:this.container;this.hideElement.hidden=!0;const t=document.createElement("div");t.className="popup";const r=this.color;if(r){const e=.7*(255-r[0])+r[0],s=.7*(255-r[1])+r[1],a=.7*(255-r[2])+r[2];t.style.backgroundColor=n.Util.makeHexColor(0|e,0|s,0|a)}const a=document.createElement("h1");a.textContent=this.title;t.appendChild(a);const i=s.PDFDateString.toDateObject(this.modificationDate);if(i){const e=document.createElement("span");e.textContent="{{date}}, {{time}}";e.dataset.l10nId="annotation_date_string";e.dataset.l10nArgs=JSON.stringify({date:i.toLocaleDateString(),time:i.toLocaleTimeString()});t.appendChild(e)}const o=this._formatContents(this.contents);t.appendChild(o);Array.isArray(this.trigger)||(this.trigger=[this.trigger]);for(const e of this.trigger){e.addEventListener("click",this._toggle.bind(this));e.addEventListener("mouseover",this._show.bind(this,!1));e.addEventListener("mouseout",this._hide.bind(this,!1))}t.addEventListener("click",this._hide.bind(this,!0));e.appendChild(t);return e}_formatContents(e){const t=document.createElement("p"),r=e.split(/(?:\r\n?|\n)/);for(let e=0,s=r.length;e<s;++e){const n=r[e];t.appendChild(document.createTextNode(n));e<s-1&&t.appendChild(document.createElement("br"))}return t}_toggle(){this.pinned?this._hide(!0):this._show(!0)}_show(e=!1){e&&(this.pinned=!0);if(this.hideElement.hidden){this.hideElement.hidden=!1;this.container.style.zIndex+=1}}_hide(e=!0){e&&(this.pinned=!1);if(!this.hideElement.hidden&&!this.pinned){this.hideElement.hidden=!0;this.container.style.zIndex-=1}}}class FreeTextAnnotationElement extends AnnotationElement{constructor(e){super(e,{isRenderable:!!(e.data.hasPopup||e.data.title||e.data.contents),ignoreBorder:!0})}render(){this.container.className="freeTextAnnotation";this.data.hasPopup||this._createPopup(null,this.data);return this.container}}class LineAnnotationElement extends AnnotationElement{constructor(e){super(e,{isRenderable:!!(e.data.hasPopup||e.data.title||e.data.contents),ignoreBorder:!0})}render(){this.container.className="lineAnnotation";const e=this.data,t=e.rect[2]-e.rect[0],r=e.rect[3]-e.rect[1],s=this.svgFactory.create(t,r),n=this.svgFactory.createElement("svg:line");n.setAttribute("x1",e.rect[2]-e.lineCoordinates[0]);n.setAttribute("y1",e.rect[3]-e.lineCoordinates[1]);n.setAttribute("x2",e.rect[2]-e.lineCoordinates[2]);n.setAttribute("y2",e.rect[3]-e.lineCoordinates[3]);n.setAttribute("stroke-width",e.borderStyle.width||1);n.setAttribute("stroke","transparent");s.appendChild(n);this.container.append(s);this._createPopup(n,e);return this.container}}class SquareAnnotationElement extends AnnotationElement{constructor(e){super(e,{isRenderable:!!(e.data.hasPopup||e.data.title||e.data.contents),ignoreBorder:!0})}render(){this.container.className="squareAnnotation";const e=this.data,t=e.rect[2]-e.rect[0],r=e.rect[3]-e.rect[1],s=this.svgFactory.create(t,r),n=e.borderStyle.width,a=this.svgFactory.createElement("svg:rect");a.setAttribute("x",n/2);a.setAttribute("y",n/2);a.setAttribute("width",t-n);a.setAttribute("height",r-n);a.setAttribute("stroke-width",n||1);a.setAttribute("stroke","transparent");a.setAttribute("fill","none");s.appendChild(a);this.container.append(s);this._createPopup(a,e);return this.container}}class CircleAnnotationElement extends AnnotationElement{constructor(e){super(e,{isRenderable:!!(e.data.hasPopup||e.data.title||e.data.contents),ignoreBorder:!0})}render(){this.container.className="circleAnnotation";const e=this.data,t=e.rect[2]-e.rect[0],r=e.rect[3]-e.rect[1],s=this.svgFactory.create(t,r),n=e.borderStyle.width,a=this.svgFactory.createElement("svg:ellipse");a.setAttribute("cx",t/2);a.setAttribute("cy",r/2);a.setAttribute("rx",t/2-n/2);a.setAttribute("ry",r/2-n/2);a.setAttribute("stroke-width",n||1);a.setAttribute("stroke","transparent");a.setAttribute("fill","none");s.appendChild(a);this.container.append(s);this._createPopup(a,e);return this.container}}class PolylineAnnotationElement extends AnnotationElement{constructor(e){super(e,{isRenderable:!!(e.data.hasPopup||e.data.title||e.data.contents),ignoreBorder:!0});this.containerClassName="polylineAnnotation";this.svgElementName="svg:polyline"}render(){this.container.className=this.containerClassName;const e=this.data,t=e.rect[2]-e.rect[0],r=e.rect[3]-e.rect[1],s=this.svgFactory.create(t,r);let n=[];for(const t of e.vertices){const r=t.x-e.rect[0],s=e.rect[3]-t.y;n.push(r+","+s)}n=n.join(" ");const a=this.svgFactory.createElement(this.svgElementName);a.setAttribute("points",n);a.setAttribute("stroke-width",e.borderStyle.width||1);a.setAttribute("stroke","transparent");a.setAttribute("fill","none");s.appendChild(a);this.container.append(s);this._createPopup(a,e);return this.container}}class PolygonAnnotationElement extends PolylineAnnotationElement{constructor(e){super(e);this.containerClassName="polygonAnnotation";this.svgElementName="svg:polygon"}}class CaretAnnotationElement extends AnnotationElement{constructor(e){super(e,{isRenderable:!!(e.data.hasPopup||e.data.title||e.data.contents),ignoreBorder:!0})}render(){this.container.className="caretAnnotation";this.data.hasPopup||this._createPopup(null,this.data);return this.container}}class InkAnnotationElement extends AnnotationElement{constructor(e){super(e,{isRenderable:!!(e.data.hasPopup||e.data.title||e.data.contents),ignoreBorder:!0});this.containerClassName="inkAnnotation";this.svgElementName="svg:polyline"}render(){this.container.className=this.containerClassName;const e=this.data,t=e.rect[2]-e.rect[0],r=e.rect[3]-e.rect[1],s=this.svgFactory.create(t,r);for(const t of e.inkLists){let r=[];for(const s of t){const t=s.x-e.rect[0],n=e.rect[3]-s.y;r.push(`${t},${n}`)}r=r.join(" ");const n=this.svgFactory.createElement(this.svgElementName);n.setAttribute("points",r);n.setAttribute("stroke-width",e.borderStyle.width||1);n.setAttribute("stroke","transparent");n.setAttribute("fill","none");this._createPopup(n,e);s.appendChild(n)}this.container.append(s);return this.container}}class HighlightAnnotationElement extends AnnotationElement{constructor(e){super(e,{isRenderable:!!(e.data.hasPopup||e.data.title||e.data.contents),ignoreBorder:!0,createQuadrilaterals:!0})}render(){this.data.hasPopup||this._createPopup(null,this.data);if(this.quadrilaterals)return this._renderQuadrilaterals("highlightAnnotation");this.container.className="highlightAnnotation";return this.container}}class UnderlineAnnotationElement extends AnnotationElement{constructor(e){super(e,{isRenderable:!!(e.data.hasPopup||e.data.title||e.data.contents),ignoreBorder:!0,createQuadrilaterals:!0})}render(){this.data.hasPopup||this._createPopup(null,this.data);if(this.quadrilaterals)return this._renderQuadrilaterals("underlineAnnotation");this.container.className="underlineAnnotation";return this.container}}class SquigglyAnnotationElement extends AnnotationElement{constructor(e){super(e,{isRenderable:!!(e.data.hasPopup||e.data.title||e.data.contents),ignoreBorder:!0,createQuadrilaterals:!0})}render(){this.data.hasPopup||this._createPopup(null,this.data);if(this.quadrilaterals)return this._renderQuadrilaterals("squigglyAnnotation");this.container.className="squigglyAnnotation";return this.container}}class StrikeOutAnnotationElement extends AnnotationElement{constructor(e){super(e,{isRenderable:!!(e.data.hasPopup||e.data.title||e.data.contents),ignoreBorder:!0,createQuadrilaterals:!0})}render(){this.data.hasPopup||this._createPopup(null,this.data);if(this.quadrilaterals)return this._renderQuadrilaterals("strikeoutAnnotation");this.container.className="strikeoutAnnotation";return this.container}}class StampAnnotationElement extends AnnotationElement{constructor(e){super(e,{isRenderable:!!(e.data.hasPopup||e.data.title||e.data.contents),ignoreBorder:!0})}render(){this.container.className="stampAnnotation";this.data.hasPopup||this._createPopup(null,this.data);return this.container}}class FileAttachmentAnnotationElement extends AnnotationElement{constructor(e){super(e,{isRenderable:!0});const{filename:t,content:r}=this.data.file;this.filename=(0,s.getFilenameFromUrl)(t);this.content=r;this.linkService.eventBus?.dispatch("fileattachmentannotation",{source:this,id:(0,n.stringToPDFString)(t),filename:t,content:r})}render(){this.container.className="fileAttachmentAnnotation";const e=document.createElement("div");e.style.height=this.container.style.height;e.style.width=this.container.style.width;e.addEventListener("dblclick",this._download.bind(this));this.data.hasPopup||!this.data.title&&!this.data.contents||this._createPopup(e,this.data);this.container.appendChild(e);return this.container}_download(){this.downloadManager?.openOrDownloadData(this.container,this.content,this.filename)}}t.AnnotationLayer=class AnnotationLayer{static render(e){const t=[],r=[];for(const s of e.annotations)s&&(s.annotationType!==n.AnnotationType.POPUP?t.push(s):r.push(s));r.length&&t.push(...r);for(const r of t){const t=AnnotationElementFactory.create({data:r,layer:e.div,page:e.page,viewport:e.viewport,linkService:e.linkService,downloadManager:e.downloadManager,imageResourcesPath:e.imageResourcesPath||"",renderInteractiveForms:!1!==e.renderInteractiveForms,svgFactory:new s.DOMSVGFactory,annotationStorage:e.annotationStorage||new a.AnnotationStorage,enableScripting:e.enableScripting,hasJSActions:e.hasJSActions,mouseState:e.mouseState||{isDown:!1}});if(t.isRenderable){const s=t.render();r.hidden&&(s.style.visibility="hidden");if(Array.isArray(s))for(const t of s)e.div.appendChild(t);else t instanceof PopupAnnotationElement?e.div.prepend(s):e.div.appendChild(s)}}}static update(e){const t=`matrix(${e.viewport.transform.join(",")})`;for(const r of e.annotations){const s=e.div.querySelectorAll(`[data-annotation-id="${r.id}"]`);if(s)for(const e of s)e.style.transform=t}e.div.hidden=!1}}},(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});t.ColorConverters=void 0;function makeColorComp(e){return Math.floor(255*Math.max(0,Math.min(1,e))).toString(16).padStart(2,"0")}t.ColorConverters=class ColorConverters{static CMYK_G([e,t,r,s]){return["G",1-Math.min(1,.3*e+.59*r+.11*t+s)]}static G_CMYK([e]){return["CMYK",0,0,0,1-e]}static G_RGB([e]){return["RGB",e,e,e]}static G_HTML([e]){const t=makeColorComp(e);return`#${t}${t}${t}`}static RGB_G([e,t,r]){return["G",.3*e+.59*t+.11*r]}static RGB_HTML([e,t,r]){return`#${makeColorComp(e)}${makeColorComp(t)}${makeColorComp(r)}`}static T_HTML(){return"#00000000"}static CMYK_RGB([e,t,r,s]){return["RGB",1-Math.min(1,e+s),1-Math.min(1,r+s),1-Math.min(1,t+s)]}static CMYK_HTML(e){return this.RGB_HTML(this.CMYK_RGB(e))}static RGB_CMYK([e,t,r]){const s=1-e,n=1-t,a=1-r;return["CMYK",s,n,a,Math.min(s,n,a)]}}},(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});t.renderTextLayer=function renderTextLayer(e){const t=new TextLayerRenderTask({textContent:e.textContent,textContentStream:e.textContentStream,container:e.container,viewport:e.viewport,textDivs:e.textDivs,textContentItemsStr:e.textContentItemsStr,enhanceTextSelection:e.enhanceTextSelection});t._render(e.timeout);return t};var s=r(2);const n=30,a=new Map,i=/^\s+$/g;function appendText(e,t,r,o){const l=document.createElement("span"),c={angle:0,canvasWidth:0,hasText:""!==t.str,hasEOL:t.hasEOL,originalTransform:null,paddingBottom:0,paddingLeft:0,paddingRight:0,paddingTop:0,scale:1};e._textDivs.push(l);const h=s.Util.transform(e._viewport.transform,t.transform);let d=Math.atan2(h[1],h[0]);const u=r[t.fontName];u.vertical&&(d+=Math.PI/2);const p=Math.hypot(h[2],h[3]),g=p*function getAscent(e,t){const r=a.get(e);if(r)return r;t.save();t.font=`30px ${e}`;const s=t.measureText("");let i=s.fontBoundingBoxAscent,o=Math.abs(s.fontBoundingBoxDescent);if(i){t.restore();const r=i/(i+o);a.set(e,r);return r}t.strokeStyle="red";t.clearRect(0,0,n,n);t.strokeText("g",0,0);let l=t.getImageData(0,0,n,n).data;o=0;for(let e=l.length-1-3;e>=0;e-=4)if(l[e]>0){o=Math.ceil(e/4/n);break}t.clearRect(0,0,n,n);t.strokeText("A",0,n);l=t.getImageData(0,0,n,n).data;i=0;for(let e=0,t=l.length;e<t;e+=4)if(l[e]>0){i=n-Math.floor(e/4/n);break}t.restore();if(i){const t=i/(i+o);a.set(e,t);return t}a.set(e,.8);return.8}(u.fontFamily,o);let f,m;if(0===d){f=h[4];m=h[5]-g}else{f=h[4]+g*Math.sin(d);m=h[5]-g*Math.cos(d)}l.style.left=`${f}px`;l.style.top=`${m}px`;l.style.fontSize=`${p}px`;l.style.fontFamily=u.fontFamily;l.setAttribute("role","presentation");l.textContent=t.str;l.dir=t.dir;e._fontInspectorEnabled&&(l.dataset.fontName=t.fontName);0!==d&&(c.angle=d*(180/Math.PI));let A=!1;if(t.str.length>1||e._enhanceTextSelection&&i.test(t.str))A=!0;else if(t.transform[0]!==t.transform[3]){const e=Math.abs(t.transform[0]),r=Math.abs(t.transform[3]);e!==r&&Math.max(e,r)/Math.min(e,r)>1.5&&(A=!0)}A&&(u.vertical?c.canvasWidth=t.height*e._viewport.scale:c.canvasWidth=t.width*e._viewport.scale);e._textDivProperties.set(l,c);e._textContentStream&&e._layoutText(l);if(e._enhanceTextSelection&&c.hasText){let r=1,n=0;if(0!==d){r=Math.cos(d);n=Math.sin(d)}const a=(u.vertical?t.height:t.width)*e._viewport.scale,i=p;let o,c;if(0!==d){o=[r,n,-n,r,f,m];c=s.Util.getAxialAlignedBoundingBox([0,0,a,i],o)}else c=[f,m,f+a,m+i];e._bounds.push({left:c[0],top:c[1],right:c[2],bottom:c[3],div:l,size:[a,i],m:o})}}function render(e){if(e._canceled)return;const t=e._textDivs,r=e._capability,s=t.length;if(s>1e5){e._renderingDone=!0;r.resolve()}else{if(!e._textContentStream)for(let r=0;r<s;r++)e._layoutText(t[r]);e._renderingDone=!0;r.resolve()}}function findPositiveMin(e,t,r){let s=0;for(let n=0;n<r;n++){const r=e[t++];r>0&&(s=s?Math.min(r,s):r)}return s}function expand(e){const t=e._bounds,r=e._viewport,n=function expandBounds(e,t,r){const s=r.map((function(e,t){return{x1:e.left,y1:e.top,x2:e.right,y2:e.bottom,index:t,x1New:void 0,x2New:void 0}}));expandBoundsLTR(e,s);const n=new Array(r.length);for(const e of s){const t=e.index;n[t]={left:e.x1New,top:0,right:e.x2New,bottom:0}}r.map((function(t,r){const a=n[r],i=s[r];i.x1=t.top;i.y1=e-a.right;i.x2=t.bottom;i.y2=e-a.left;i.index=r;i.x1New=void 0;i.x2New=void 0}));expandBoundsLTR(t,s);for(const e of s){const t=e.index;n[t].top=e.x1New;n[t].bottom=e.x2New}return n}(r.width,r.height,t);for(let r=0;r<n.length;r++){const a=t[r].div,i=e._textDivProperties.get(a);if(0===i.angle){i.paddingLeft=t[r].left-n[r].left;i.paddingTop=t[r].top-n[r].top;i.paddingRight=n[r].right-t[r].right;i.paddingBottom=n[r].bottom-t[r].bottom;e._textDivProperties.set(a,i);continue}const o=n[r],l=t[r],c=l.m,h=c[0],d=c[1],u=[[0,0],[0,l.size[1]],[l.size[0],0],l.size],p=new Float64Array(64);for(let e=0,t=u.length;e<t;e++){const t=s.Util.applyTransform(u[e],c);p[e+0]=h&&(o.left-t[0])/h;p[e+4]=d&&(o.top-t[1])/d;p[e+8]=h&&(o.right-t[0])/h;p[e+12]=d&&(o.bottom-t[1])/d;p[e+16]=d&&(o.left-t[0])/-d;p[e+20]=h&&(o.top-t[1])/h;p[e+24]=d&&(o.right-t[0])/-d;p[e+28]=h&&(o.bottom-t[1])/h;p[e+32]=h&&(o.left-t[0])/-h;p[e+36]=d&&(o.top-t[1])/-d;p[e+40]=h&&(o.right-t[0])/-h;p[e+44]=d&&(o.bottom-t[1])/-d;p[e+48]=d&&(o.left-t[0])/d;p[e+52]=h&&(o.top-t[1])/-h;p[e+56]=d&&(o.right-t[0])/d;p[e+60]=h&&(o.bottom-t[1])/-h}const g=1+Math.min(Math.abs(h),Math.abs(d));i.paddingLeft=findPositiveMin(p,32,16)/g;i.paddingTop=findPositiveMin(p,48,16)/g;i.paddingRight=findPositiveMin(p,0,16)/g;i.paddingBottom=findPositiveMin(p,16,16)/g;e._textDivProperties.set(a,i)}}function expandBoundsLTR(e,t){t.sort((function(e,t){return e.x1-t.x1||e.index-t.index}));const r=[{start:-1/0,end:1/0,boundary:{x1:-1/0,y1:-1/0,x2:0,y2:1/0,index:-1,x1New:0,x2New:0}}];for(const e of t){let t=0;for(;t<r.length&&r[t].end<=e.y1;)t++;let s,n,a=r.length-1;for(;a>=0&&r[a].start>=e.y2;)a--;let i,o,l=-1/0;for(i=t;i<=a;i++){s=r[i];n=s.boundary;let t;t=n.x2>e.x1?n.index>e.index?n.x1New:e.x1:void 0===n.x2New?(n.x2+e.x1)/2:n.x2New;t>l&&(l=t)}e.x1New=l;for(i=t;i<=a;i++){s=r[i];n=s.boundary;void 0===n.x2New?n.x2>e.x1?n.index>e.index&&(n.x2New=n.x2):n.x2New=l:n.x2New>l&&(n.x2New=Math.max(l,n.x2))}const c=[];let h=null;for(i=t;i<=a;i++){s=r[i];n=s.boundary;const t=n.x2>e.x2?n:e;if(h===t)c[c.length-1].end=s.end;else{c.push({start:s.start,end:s.end,boundary:t});h=t}}if(r[t].start<e.y1){c[0].start=e.y1;c.unshift({start:r[t].start,end:e.y1,boundary:r[t].boundary})}if(e.y2<r[a].end){c[c.length-1].end=e.y2;c.push({start:e.y2,end:r[a].end,boundary:r[a].boundary})}for(i=t;i<=a;i++){s=r[i];n=s.boundary;if(void 0!==n.x2New)continue;let e=!1;for(o=t-1;!e&&o>=0&&r[o].start>=n.y1;o--)e=r[o].boundary===n;for(o=a+1;!e&&o<r.length&&r[o].end<=n.y2;o++)e=r[o].boundary===n;for(o=0;!e&&o<c.length;o++)e=c[o].boundary===n;e||(n.x2New=l)}Array.prototype.splice.apply(r,[t,a-t+1].concat(c))}for(const t of r){const r=t.boundary;void 0===r.x2New&&(r.x2New=Math.max(e,r.x2))}}class TextLayerRenderTask{constructor({textContent:e,textContentStream:t,container:r,viewport:n,textDivs:a,textContentItemsStr:i,enhanceTextSelection:o}){this._textContent=e;this._textContentStream=t;this._container=r;this._document=r.ownerDocument;this._viewport=n;this._textDivs=a||[];this._textContentItemsStr=i||[];this._enhanceTextSelection=!!o;this._fontInspectorEnabled=!!globalThis.FontInspector?.enabled;this._reader=null;this._layoutTextLastFontSize=null;this._layoutTextLastFontFamily=null;this._layoutTextCtx=null;this._textDivProperties=new WeakMap;this._renderingDone=!1;this._canceled=!1;this._capability=(0,s.createPromiseCapability)();this._renderTimer=null;this._bounds=[];this._capability.promise.finally((()=>{if(this._layoutTextCtx){this._layoutTextCtx.canvas.width=0;this._layoutTextCtx.canvas.height=0;this._layoutTextCtx=null}})).catch((()=>{}))}get promise(){return this._capability.promise}cancel(){this._canceled=!0;if(this._reader){this._reader.cancel(new s.AbortException("TextLayer task cancelled."));this._reader=null}if(null!==this._renderTimer){clearTimeout(this._renderTimer);this._renderTimer=null}this._capability.reject(new Error("TextLayer task cancelled."))}_processItems(e,t){for(let r=0,s=e.length;r<s;r++)if(void 0!==e[r].str){this._textContentItemsStr.push(e[r].str);appendText(this,e[r],t,this._layoutTextCtx)}else if("beginMarkedContentProps"===e[r].type||"beginMarkedContent"===e[r].type){const t=this._container;this._container=document.createElement("span");this._container.classList.add("markedContent");null!==e[r].id&&this._container.setAttribute("id",`${e[r].id}`);t.appendChild(this._container)}else"endMarkedContent"===e[r].type&&(this._container=this._container.parentNode)}_layoutText(e){const t=this._textDivProperties.get(e);let r="";if(0!==t.canvasWidth&&t.hasText){const{fontSize:s,fontFamily:n}=e.style;if(s!==this._layoutTextLastFontSize||n!==this._layoutTextLastFontFamily){this._layoutTextCtx.font=`${s} ${n}`;this._layoutTextLastFontSize=s;this._layoutTextLastFontFamily=n}const{width:a}=this._layoutTextCtx.measureText(e.textContent);if(a>0){t.scale=t.canvasWidth/a;r=`scaleX(${t.scale})`}}0!==t.angle&&(r=`rotate(${t.angle}deg) ${r}`);if(r.length>0){this._enhanceTextSelection&&(t.originalTransform=r);e.style.transform=r}t.hasText&&this._container.appendChild(e);if(t.hasEOL){const e=document.createElement("br");e.setAttribute("role","presentation");this._container.appendChild(e)}}_render(e=0){const t=(0,s.createPromiseCapability)();let r=Object.create(null);const a=this._document.createElement("canvas");a.height=a.width=n;a.mozOpaque=!0;this._layoutTextCtx=a.getContext("2d",{alpha:!1});if(this._textContent){const e=this._textContent.items,r=this._textContent.styles;this._processItems(e,r);t.resolve()}else{if(!this._textContentStream)throw new Error('Neither "textContent" nor "textContentStream" parameters specified.');{const pump=()=>{this._reader.read().then((({value:e,done:s})=>{if(s)t.resolve();else{Object.assign(r,e.styles);this._processItems(e.items,r);pump()}}),t.reject)};this._reader=this._textContentStream.getReader();pump()}}t.promise.then((()=>{r=null;e?this._renderTimer=setTimeout((()=>{render(this);this._renderTimer=null}),e):render(this)}),this._capability.reject)}expandTextDivs(e=!1){if(!this._enhanceTextSelection||!this._renderingDone)return;if(null!==this._bounds){expand(this);this._bounds=null}const t=[],r=[];for(let s=0,n=this._textDivs.length;s<n;s++){const n=this._textDivs[s],a=this._textDivProperties.get(n);if(a.hasText)if(e){t.length=0;r.length=0;a.originalTransform&&t.push(a.originalTransform);if(a.paddingTop>0){r.push(`${a.paddingTop}px`);t.push(`translateY(${-a.paddingTop}px)`)}else r.push(0);a.paddingRight>0?r.push(a.paddingRight/a.scale+"px"):r.push(0);a.paddingBottom>0?r.push(`${a.paddingBottom}px`):r.push(0);if(a.paddingLeft>0){r.push(a.paddingLeft/a.scale+"px");t.push(`translateX(${-a.paddingLeft/a.scale}px)`)}else r.push(0);n.style.padding=r.join(" ");t.length&&(n.style.transform=t.join(" "))}else{n.style.padding=null;n.style.transform=a.originalTransform}}}}},(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});t.SVGGraphics=void 0;var s=r(2),n=r(1),a=r(4);let i=class{constructor(){(0,s.unreachable)("Not implemented: SVGGraphics")}};t.SVGGraphics=i;{const e={fontStyle:"normal",fontWeight:"normal",fillColor:"#000000"},r="http://www.w3.org/XML/1998/namespace",o="http://www.w3.org/1999/xlink",l=["butt","round","square"],c=["miter","round","bevel"],h=function(){const e=new Uint8Array([137,80,78,71,13,10,26,10]),t=new Int32Array(256);for(let e=0;e<256;e++){let r=e;for(let e=0;e<8;e++)r=1&r?3988292384^r>>1&2147483647:r>>1&2147483647;t[e]=r}function writePngChunk(e,r,s,n){let a=n;const i=r.length;s[a]=i>>24&255;s[a+1]=i>>16&255;s[a+2]=i>>8&255;s[a+3]=255&i;a+=4;s[a]=255&e.charCodeAt(0);s[a+1]=255&e.charCodeAt(1);s[a+2]=255&e.charCodeAt(2);s[a+3]=255&e.charCodeAt(3);a+=4;s.set(r,a);a+=r.length;const o=function crc32(e,r,s){let n=-1;for(let a=r;a<s;a++){const r=255&(n^e[a]);n=n>>>8^t[r]}return-1^n}(s,n+4,a);s[a]=o>>24&255;s[a+1]=o>>16&255;s[a+2]=o>>8&255;s[a+3]=255&o}function deflateSyncUncompressed(e){let t=e.length;const r=65535,s=Math.ceil(t/r),n=new Uint8Array(2+t+5*s+4);let a=0;n[a++]=120;n[a++]=156;let i=0;for(;t>r;){n[a++]=0;n[a++]=255;n[a++]=255;n[a++]=0;n[a++]=0;n.set(e.subarray(i,i+r),a);a+=r;i+=r;t-=r}n[a++]=1;n[a++]=255&t;n[a++]=t>>8&255;n[a++]=255&~t;n[a++]=(65535&~t)>>8&255;n.set(e.subarray(i),a);a+=e.length-i;const o=function adler32(e,t,r){let s=1,n=0;for(let a=t;a<r;++a){s=(s+(255&e[a]))%65521;n=(n+s)%65521}return n<<16|s}(e,0,e.length);n[a++]=o>>24&255;n[a++]=o>>16&255;n[a++]=o>>8&255;n[a++]=255&o;return n}function encode(t,r,n,i){const o=t.width,l=t.height;let c,h,d;const u=t.data;switch(r){case s.ImageKind.GRAYSCALE_1BPP:h=0;c=1;d=o+7>>3;break;case s.ImageKind.RGB_24BPP:h=2;c=8;d=3*o;break;case s.ImageKind.RGBA_32BPP:h=6;c=8;d=4*o;break;default:throw new Error("invalid format")}const p=new Uint8Array((1+d)*l);let g=0,f=0;for(let e=0;e<l;++e){p[g++]=0;p.set(u.subarray(f,f+d),g);f+=d;g+=d}if(r===s.ImageKind.GRAYSCALE_1BPP&&i){g=0;for(let e=0;e<l;e++){g++;for(let e=0;e<d;e++)p[g++]^=255}}const m=new Uint8Array([o>>24&255,o>>16&255,o>>8&255,255&o,l>>24&255,l>>16&255,l>>8&255,255&l,c,h,0,0,0]),A=function deflateSync(e){if(!a.isNodeJS)return deflateSyncUncompressed(e);try{let t;t=parseInt(process.versions.node)>=8?e:Buffer.from(e);const r=require("zlib").deflateSync(t,{level:9});return r instanceof Uint8Array?r:new Uint8Array(r)}catch(e){(0,s.warn)("Not compressing PNG because zlib.deflateSync is unavailable: "+e)}return deflateSyncUncompressed(e)}(p),_=e.length+36+m.length+A.length,b=new Uint8Array(_);let y=0;b.set(e,y);y+=e.length;writePngChunk("IHDR",m,b,y);y+=12+m.length;writePngChunk("IDATA",A,b,y);y+=12+A.length;writePngChunk("IEND",new Uint8Array(0),b,y);return(0,s.createObjectURL)(b,"image/png",n)}return function convertImgDataToPng(e,t,r){return encode(e,void 0===e.kind?s.ImageKind.GRAYSCALE_1BPP:e.kind,t,r)}}();class SVGExtraState{constructor(){this.fontSizeScale=1;this.fontWeight=e.fontWeight;this.fontSize=0;this.textMatrix=s.IDENTITY_MATRIX;this.fontMatrix=s.FONT_IDENTITY_MATRIX;this.leading=0;this.textRenderingMode=s.TextRenderingMode.FILL;this.textMatrixScale=1;this.x=0;this.y=0;this.lineX=0;this.lineY=0;this.charSpacing=0;this.wordSpacing=0;this.textHScale=1;this.textRise=0;this.fillColor=e.fillColor;this.strokeColor="#000000";this.fillAlpha=1;this.strokeAlpha=1;this.lineWidth=1;this.lineJoin="";this.lineCap="";this.miterLimit=0;this.dashArray=[];this.dashPhase=0;this.dependencies=[];this.activeClipUrl=null;this.clipGroup=null;this.maskId=""}clone(){return Object.create(this)}setCurrentPoint(e,t){this.x=e;this.y=t}}function opListToTree(e){let t=[];const r=[];for(const s of e)if("save"!==s.fn)"restore"===s.fn?t=r.pop():t.push(s);else{t.push({fnId:92,fn:"group",items:[]});r.push(t);t=t[t.length-1].items}return t}function pf(e){if(Number.isInteger(e))return e.toString();const t=e.toFixed(10);let r=t.length-1;if("0"!==t[r])return t;do{r--}while("0"===t[r]);return t.substring(0,"."===t[r]?r:r+1)}function pm(e){if(0===e[4]&&0===e[5]){if(0===e[1]&&0===e[2])return 1===e[0]&&1===e[3]?"":`scale(${pf(e[0])} ${pf(e[3])})`;if(e[0]===e[3]&&e[1]===-e[2]){return`rotate(${pf(180*Math.acos(e[0])/Math.PI)})`}}else if(1===e[0]&&0===e[1]&&0===e[2]&&1===e[3])return`translate(${pf(e[4])} ${pf(e[5])})`;return`matrix(${pf(e[0])} ${pf(e[1])} ${pf(e[2])} ${pf(e[3])} ${pf(e[4])} ${pf(e[5])})`}let d=0,u=0,p=0;t.SVGGraphics=i=class{constructor(e,t,r=!1){this.svgFactory=new n.DOMSVGFactory;this.current=new SVGExtraState;this.transformMatrix=s.IDENTITY_MATRIX;this.transformStack=[];this.extraStack=[];this.commonObjs=e;this.objs=t;this.pendingClip=null;this.pendingEOFill=!1;this.embedFonts=!1;this.embeddedFonts=Object.create(null);this.cssStyle=null;this.forceDataSchema=!!r;this._operatorIdMapping=[];for(const e in s.OPS)this._operatorIdMapping[s.OPS[e]]=e}save(){this.transformStack.push(this.transformMatrix);const e=this.current;this.extraStack.push(e);this.current=e.clone()}restore(){this.transformMatrix=this.transformStack.pop();this.current=this.extraStack.pop();this.pendingClip=null;this.tgrp=null}group(e){this.save();this.executeOpTree(e);this.restore()}loadDependencies(e){const t=e.fnArray,r=e.argsArray;for(let e=0,n=t.length;e<n;e++)if(t[e]===s.OPS.dependency)for(const t of r[e]){const e=t.startsWith("g_")?this.commonObjs:this.objs,r=new Promise((r=>{e.get(t,r)}));this.current.dependencies.push(r)}return Promise.all(this.current.dependencies)}transform(e,t,r,n,a,i){const o=[e,t,r,n,a,i];this.transformMatrix=s.Util.transform(this.transformMatrix,o);this.tgrp=null}getSVG(e,t){this.viewport=t;const r=this._initialize(t);return this.loadDependencies(e).then((()=>{this.transformMatrix=s.IDENTITY_MATRIX;this.executeOpTree(this.convertOpList(e));return r}))}convertOpList(e){const t=this._operatorIdMapping,r=e.argsArray,s=e.fnArray,n=[];for(let e=0,a=s.length;e<a;e++){const a=s[e];n.push({fnId:a,fn:t[a],args:r[e]})}return opListToTree(n)}executeOpTree(e){for(const t of e){const e=t.fn,r=t.fnId,n=t.args;switch(0|r){case s.OPS.beginText:this.beginText();break;case s.OPS.dependency:break;case s.OPS.setLeading:this.setLeading(n);break;case s.OPS.setLeadingMoveText:this.setLeadingMoveText(n[0],n[1]);break;case s.OPS.setFont:this.setFont(n);break;case s.OPS.showText:case s.OPS.showSpacedText:this.showText(n[0]);break;case s.OPS.endText:this.endText();break;case s.OPS.moveText:this.moveText(n[0],n[1]);break;case s.OPS.setCharSpacing:this.setCharSpacing(n[0]);break;case s.OPS.setWordSpacing:this.setWordSpacing(n[0]);break;case s.OPS.setHScale:this.setHScale(n[0]);break;case s.OPS.setTextMatrix:this.setTextMatrix(n[0],n[1],n[2],n[3],n[4],n[5]);break;case s.OPS.setTextRise:this.setTextRise(n[0]);break;case s.OPS.setTextRenderingMode:this.setTextRenderingMode(n[0]);break;case s.OPS.setLineWidth:this.setLineWidth(n[0]);break;case s.OPS.setLineJoin:this.setLineJoin(n[0]);break;case s.OPS.setLineCap:this.setLineCap(n[0]);break;case s.OPS.setMiterLimit:this.setMiterLimit(n[0]);break;case s.OPS.setFillRGBColor:this.setFillRGBColor(n[0],n[1],n[2]);break;case s.OPS.setStrokeRGBColor:this.setStrokeRGBColor(n[0],n[1],n[2]);break;case s.OPS.setStrokeColorN:this.setStrokeColorN(n);break;case s.OPS.setFillColorN:this.setFillColorN(n);break;case s.OPS.shadingFill:this.shadingFill(n[0]);break;case s.OPS.setDash:this.setDash(n[0],n[1]);break;case s.OPS.setRenderingIntent:this.setRenderingIntent(n[0]);break;case s.OPS.setFlatness:this.setFlatness(n[0]);break;case s.OPS.setGState:this.setGState(n[0]);break;case s.OPS.fill:this.fill();break;case s.OPS.eoFill:this.eoFill();break;case s.OPS.stroke:this.stroke();break;case s.OPS.fillStroke:this.fillStroke();break;case s.OPS.eoFillStroke:this.eoFillStroke();break;case s.OPS.clip:this.clip("nonzero");break;case s.OPS.eoClip:this.clip("evenodd");break;case s.OPS.paintSolidColorImageMask:this.paintSolidColorImageMask();break;case s.OPS.paintImageXObject:this.paintImageXObject(n[0]);break;case s.OPS.paintInlineImageXObject:this.paintInlineImageXObject(n[0]);break;case s.OPS.paintImageMaskXObject:this.paintImageMaskXObject(n[0]);break;case s.OPS.paintFormXObjectBegin:this.paintFormXObjectBegin(n[0],n[1]);break;case s.OPS.paintFormXObjectEnd:this.paintFormXObjectEnd();break;case s.OPS.closePath:this.closePath();break;case s.OPS.closeStroke:this.closeStroke();break;case s.OPS.closeFillStroke:this.closeFillStroke();break;case s.OPS.closeEOFillStroke:this.closeEOFillStroke();break;case s.OPS.nextLine:this.nextLine();break;case s.OPS.transform:this.transform(n[0],n[1],n[2],n[3],n[4],n[5]);break;case s.OPS.constructPath:this.constructPath(n[0],n[1]);break;case s.OPS.endPath:this.endPath();break;case 92:this.group(t.items);break;default:(0,s.warn)(`Unimplemented operator ${e}`)}}}setWordSpacing(e){this.current.wordSpacing=e}setCharSpacing(e){this.current.charSpacing=e}nextLine(){this.moveText(0,this.current.leading)}setTextMatrix(e,t,r,s,n,a){const i=this.current;i.textMatrix=i.lineMatrix=[e,t,r,s,n,a];i.textMatrixScale=Math.hypot(e,t);i.x=i.lineX=0;i.y=i.lineY=0;i.xcoords=[];i.ycoords=[];i.tspan=this.svgFactory.createElement("svg:tspan");i.tspan.setAttributeNS(null,"font-family",i.fontFamily);i.tspan.setAttributeNS(null,"font-size",`${pf(i.fontSize)}px`);i.tspan.setAttributeNS(null,"y",pf(-i.y));i.txtElement=this.svgFactory.createElement("svg:text");i.txtElement.appendChild(i.tspan)}beginText(){const e=this.current;e.x=e.lineX=0;e.y=e.lineY=0;e.textMatrix=s.IDENTITY_MATRIX;e.lineMatrix=s.IDENTITY_MATRIX;e.textMatrixScale=1;e.tspan=this.svgFactory.createElement("svg:tspan");e.txtElement=this.svgFactory.createElement("svg:text");e.txtgrp=this.svgFactory.createElement("svg:g");e.xcoords=[];e.ycoords=[]}moveText(e,t){const r=this.current;r.x=r.lineX+=e;r.y=r.lineY+=t;r.xcoords=[];r.ycoords=[];r.tspan=this.svgFactory.createElement("svg:tspan");r.tspan.setAttributeNS(null,"font-family",r.fontFamily);r.tspan.setAttributeNS(null,"font-size",`${pf(r.fontSize)}px`);r.tspan.setAttributeNS(null,"y",pf(-r.y))}showText(t){const n=this.current,a=n.font,i=n.fontSize;if(0===i)return;const o=n.fontSizeScale,l=n.charSpacing,c=n.wordSpacing,h=n.fontDirection,d=n.textHScale*h,u=a.vertical,p=u?1:-1,g=a.defaultVMetrics,f=i*n.fontMatrix[0];let m=0;for(const e of t){if(null===e){m+=h*c;continue}if((0,s.isNum)(e)){m+=p*e*i/1e3;continue}const t=(e.isSpace?c:0)+l,r=e.fontChar;let d,A,_,b=e.width;if(u){let t;const r=e.vmetric||g;t=e.vmetric?r[1]:.5*b;t=-t*f;const s=r[2]*f;b=r?-r[0]:b;d=t/o;A=(m+s)/o}else{d=m/o;A=0}if(e.isInFont||a.missingFile){n.xcoords.push(n.x+d);u&&n.ycoords.push(-n.y+A);n.tspan.textContent+=r}_=u?b*f-t*h:b*f+t*h;m+=_}n.tspan.setAttributeNS(null,"x",n.xcoords.map(pf).join(" "));u?n.tspan.setAttributeNS(null,"y",n.ycoords.map(pf).join(" ")):n.tspan.setAttributeNS(null,"y",pf(-n.y));u?n.y-=m:n.x+=m*d;n.tspan.setAttributeNS(null,"font-family",n.fontFamily);n.tspan.setAttributeNS(null,"font-size",`${pf(n.fontSize)}px`);n.fontStyle!==e.fontStyle&&n.tspan.setAttributeNS(null,"font-style",n.fontStyle);n.fontWeight!==e.fontWeight&&n.tspan.setAttributeNS(null,"font-weight",n.fontWeight);const A=n.textRenderingMode&s.TextRenderingMode.FILL_STROKE_MASK;if(A===s.TextRenderingMode.FILL||A===s.TextRenderingMode.FILL_STROKE){n.fillColor!==e.fillColor&&n.tspan.setAttributeNS(null,"fill",n.fillColor);n.fillAlpha<1&&n.tspan.setAttributeNS(null,"fill-opacity",n.fillAlpha)}else n.textRenderingMode===s.TextRenderingMode.ADD_TO_PATH?n.tspan.setAttributeNS(null,"fill","transparent"):n.tspan.setAttributeNS(null,"fill","none");if(A===s.TextRenderingMode.STROKE||A===s.TextRenderingMode.FILL_STROKE){const e=1/(n.textMatrixScale||1);this._setStrokeAttributes(n.tspan,e)}let _=n.textMatrix;if(0!==n.textRise){_=_.slice();_[5]+=n.textRise}n.txtElement.setAttributeNS(null,"transform",`${pm(_)} scale(${pf(d)}, -1)`);n.txtElement.setAttributeNS(r,"xml:space","preserve");n.txtElement.appendChild(n.tspan);n.txtgrp.appendChild(n.txtElement);this._ensureTransformGroup().appendChild(n.txtElement)}setLeadingMoveText(e,t){this.setLeading(-t);this.moveText(e,t)}addFontStyle(e){if(!e.data)throw new Error('addFontStyle: No font data available, ensure that the "fontExtraProperties" API parameter is set.');if(!this.cssStyle){this.cssStyle=this.svgFactory.createElement("svg:style");this.cssStyle.setAttributeNS(null,"type","text/css");this.defs.appendChild(this.cssStyle)}const t=(0,s.createObjectURL)(e.data,e.mimetype,this.forceDataSchema);this.cssStyle.textContent+=`@font-face { font-family: "${e.loadedName}"; src: url(${t}); }\n`}setFont(e){const t=this.current,r=this.commonObjs.get(e[0]);let n=e[1];t.font=r;if(this.embedFonts&&!r.missingFile&&!this.embeddedFonts[r.loadedName]){this.addFontStyle(r);this.embeddedFonts[r.loadedName]=r}t.fontMatrix=r.fontMatrix||s.FONT_IDENTITY_MATRIX;let a="normal";r.black?a="900":r.bold&&(a="bold");const i=r.italic?"italic":"normal";if(n<0){n=-n;t.fontDirection=-1}else t.fontDirection=1;t.fontSize=n;t.fontFamily=r.loadedName;t.fontWeight=a;t.fontStyle=i;t.tspan=this.svgFactory.createElement("svg:tspan");t.tspan.setAttributeNS(null,"y",pf(-t.y));t.xcoords=[];t.ycoords=[]}endText(){const e=this.current;if(e.textRenderingMode&s.TextRenderingMode.ADD_TO_PATH_FLAG&&e.txtElement?.hasChildNodes()){e.element=e.txtElement;this.clip("nonzero");this.endPath()}}setLineWidth(e){e>0&&(this.current.lineWidth=e)}setLineCap(e){this.current.lineCap=l[e]}setLineJoin(e){this.current.lineJoin=c[e]}setMiterLimit(e){this.current.miterLimit=e}setStrokeAlpha(e){this.current.strokeAlpha=e}setStrokeRGBColor(e,t,r){this.current.strokeColor=s.Util.makeHexColor(e,t,r)}setFillAlpha(e){this.current.fillAlpha=e}setFillRGBColor(e,t,r){this.current.fillColor=s.Util.makeHexColor(e,t,r);this.current.tspan=this.svgFactory.createElement("svg:tspan");this.current.xcoords=[];this.current.ycoords=[]}setStrokeColorN(e){this.current.strokeColor=this._makeColorN_Pattern(e)}setFillColorN(e){this.current.fillColor=this._makeColorN_Pattern(e)}shadingFill(e){const t=this.viewport.width,r=this.viewport.height,n=s.Util.inverseTransform(this.transformMatrix),a=s.Util.applyTransform([0,0],n),i=s.Util.applyTransform([0,r],n),o=s.Util.applyTransform([t,0],n),l=s.Util.applyTransform([t,r],n),c=Math.min(a[0],i[0],o[0],l[0]),h=Math.min(a[1],i[1],o[1],l[1]),d=Math.max(a[0],i[0],o[0],l[0]),u=Math.max(a[1],i[1],o[1],l[1]),p=this.svgFactory.createElement("svg:rect");p.setAttributeNS(null,"x",c);p.setAttributeNS(null,"y",h);p.setAttributeNS(null,"width",d-c);p.setAttributeNS(null,"height",u-h);p.setAttributeNS(null,"fill",this._makeShadingPattern(e));this.current.fillAlpha<1&&p.setAttributeNS(null,"fill-opacity",this.current.fillAlpha);this._ensureTransformGroup().appendChild(p)}_makeColorN_Pattern(e){return"TilingPattern"===e[0]?this._makeTilingPattern(e):this._makeShadingPattern(e)}_makeTilingPattern(e){const t=e[1],r=e[2],n=e[3]||s.IDENTITY_MATRIX,[a,i,o,l]=e[4],c=e[5],h=e[6],d=e[7],u="shading"+p++,[g,f,m,A]=s.Util.normalizeRect([...s.Util.applyTransform([a,i],n),...s.Util.applyTransform([o,l],n)]),[_,b]=s.Util.singularValueDecompose2dScale(n),y=c*_,S=h*b,v=this.svgFactory.createElement("svg:pattern");v.setAttributeNS(null,"id",u);v.setAttributeNS(null,"patternUnits","userSpaceOnUse");v.setAttributeNS(null,"width",y);v.setAttributeNS(null,"height",S);v.setAttributeNS(null,"x",`${g}`);v.setAttributeNS(null,"y",`${f}`);const x=this.svg,C=this.transformMatrix,P=this.current.fillColor,k=this.current.strokeColor,w=this.svgFactory.create(m-g,A-f);this.svg=w;this.transformMatrix=n;if(2===d){const e=s.Util.makeHexColor(...t);this.current.fillColor=e;this.current.strokeColor=e}this.executeOpTree(this.convertOpList(r));this.svg=x;this.transformMatrix=C;this.current.fillColor=P;this.current.strokeColor=k;v.appendChild(w.childNodes[0]);this.defs.appendChild(v);return`url(#${u})`}_makeShadingPattern(e){switch(e[0]){case"RadialAxial":const t="shading"+p++,r=e[3];let n;switch(e[1]){case"axial":const r=e[4],s=e[5];n=this.svgFactory.createElement("svg:linearGradient");n.setAttributeNS(null,"id",t);n.setAttributeNS(null,"gradientUnits","userSpaceOnUse");n.setAttributeNS(null,"x1",r[0]);n.setAttributeNS(null,"y1",r[1]);n.setAttributeNS(null,"x2",s[0]);n.setAttributeNS(null,"y2",s[1]);break;case"radial":const a=e[4],i=e[5],o=e[6],l=e[7];n=this.svgFactory.createElement("svg:radialGradient");n.setAttributeNS(null,"id",t);n.setAttributeNS(null,"gradientUnits","userSpaceOnUse");n.setAttributeNS(null,"cx",i[0]);n.setAttributeNS(null,"cy",i[1]);n.setAttributeNS(null,"r",l);n.setAttributeNS(null,"fx",a[0]);n.setAttributeNS(null,"fy",a[1]);n.setAttributeNS(null,"fr",o);break;default:throw new Error(`Unknown RadialAxial type: ${e[1]}`)}for(const e of r){const t=this.svgFactory.createElement("svg:stop");t.setAttributeNS(null,"offset",e[0]);t.setAttributeNS(null,"stop-color",e[1]);n.appendChild(t)}this.defs.appendChild(n);return`url(#${t})`;case"Mesh":(0,s.warn)("Unimplemented pattern Mesh");return null;case"Dummy":return"hotpink";default:throw new Error(`Unknown IR type: ${e[0]}`)}}setDash(e,t){this.current.dashArray=e;this.current.dashPhase=t}constructPath(e,t){const r=this.current;let n=r.x,a=r.y,i=[],o=0;for(const r of e)switch(0|r){case s.OPS.rectangle:n=t[o++];a=t[o++];const e=n+t[o++],r=a+t[o++];i.push("M",pf(n),pf(a),"L",pf(e),pf(a),"L",pf(e),pf(r),"L",pf(n),pf(r),"Z");break;case s.OPS.moveTo:n=t[o++];a=t[o++];i.push("M",pf(n),pf(a));break;case s.OPS.lineTo:n=t[o++];a=t[o++];i.push("L",pf(n),pf(a));break;case s.OPS.curveTo:n=t[o+4];a=t[o+5];i.push("C",pf(t[o]),pf(t[o+1]),pf(t[o+2]),pf(t[o+3]),pf(n),pf(a));o+=6;break;case s.OPS.curveTo2:i.push("C",pf(n),pf(a),pf(t[o]),pf(t[o+1]),pf(t[o+2]),pf(t[o+3]));n=t[o+2];a=t[o+3];o+=4;break;case s.OPS.curveTo3:n=t[o+2];a=t[o+3];i.push("C",pf(t[o]),pf(t[o+1]),pf(n),pf(a),pf(n),pf(a));o+=4;break;case s.OPS.closePath:i.push("Z")}i=i.join(" ");if(r.path&&e.length>0&&e[0]!==s.OPS.rectangle&&e[0]!==s.OPS.moveTo)i=r.path.getAttributeNS(null,"d")+i;else{r.path=this.svgFactory.createElement("svg:path");this._ensureTransformGroup().appendChild(r.path)}r.path.setAttributeNS(null,"d",i);r.path.setAttributeNS(null,"fill","none");r.element=r.path;r.setCurrentPoint(n,a)}endPath(){const e=this.current;e.path=null;if(!this.pendingClip)return;if(!e.element){this.pendingClip=null;return}const t="clippath"+d++,r=this.svgFactory.createElement("svg:clipPath");r.setAttributeNS(null,"id",t);r.setAttributeNS(null,"transform",pm(this.transformMatrix));const s=e.element.cloneNode(!0);"evenodd"===this.pendingClip?s.setAttributeNS(null,"clip-rule","evenodd"):s.setAttributeNS(null,"clip-rule","nonzero");this.pendingClip=null;r.appendChild(s);this.defs.appendChild(r);if(e.activeClipUrl){e.clipGroup=null;for(const e of this.extraStack)e.clipGroup=null;r.setAttributeNS(null,"clip-path",e.activeClipUrl)}e.activeClipUrl=`url(#${t})`;this.tgrp=null}clip(e){this.pendingClip=e}closePath(){const e=this.current;if(e.path){const t=`${e.path.getAttributeNS(null,"d")}Z`;e.path.setAttributeNS(null,"d",t)}}setLeading(e){this.current.leading=-e}setTextRise(e){this.current.textRise=e}setTextRenderingMode(e){this.current.textRenderingMode=e}setHScale(e){this.current.textHScale=e/100}setRenderingIntent(e){}setFlatness(e){}setGState(e){for(const[t,r]of e)switch(t){case"LW":this.setLineWidth(r);break;case"LC":this.setLineCap(r);break;case"LJ":this.setLineJoin(r);break;case"ML":this.setMiterLimit(r);break;case"D":this.setDash(r[0],r[1]);break;case"RI":this.setRenderingIntent(r);break;case"FL":this.setFlatness(r);break;case"Font":this.setFont(r);break;case"CA":this.setStrokeAlpha(r);break;case"ca":this.setFillAlpha(r);break;default:(0,s.warn)(`Unimplemented graphic state operator ${t}`)}}fill(){const e=this.current;if(e.element){e.element.setAttributeNS(null,"fill",e.fillColor);e.element.setAttributeNS(null,"fill-opacity",e.fillAlpha);this.endPath()}}stroke(){const e=this.current;if(e.element){this._setStrokeAttributes(e.element);e.element.setAttributeNS(null,"fill","none");this.endPath()}}_setStrokeAttributes(e,t=1){const r=this.current;let s=r.dashArray;1!==t&&s.length>0&&(s=s.map((function(e){return t*e})));e.setAttributeNS(null,"stroke",r.strokeColor);e.setAttributeNS(null,"stroke-opacity",r.strokeAlpha);e.setAttributeNS(null,"stroke-miterlimit",pf(r.miterLimit));e.setAttributeNS(null,"stroke-linecap",r.lineCap);e.setAttributeNS(null,"stroke-linejoin",r.lineJoin);e.setAttributeNS(null,"stroke-width",pf(t*r.lineWidth)+"px");e.setAttributeNS(null,"stroke-dasharray",s.map(pf).join(" "));e.setAttributeNS(null,"stroke-dashoffset",pf(t*r.dashPhase)+"px")}eoFill(){this.current.element&&this.current.element.setAttributeNS(null,"fill-rule","evenodd");this.fill()}fillStroke(){this.stroke();this.fill()}eoFillStroke(){this.current.element&&this.current.element.setAttributeNS(null,"fill-rule","evenodd");this.fillStroke()}closeStroke(){this.closePath();this.stroke()}closeFillStroke(){this.closePath();this.fillStroke()}closeEOFillStroke(){this.closePath();this.eoFillStroke()}paintSolidColorImageMask(){const e=this.svgFactory.createElement("svg:rect");e.setAttributeNS(null,"x","0");e.setAttributeNS(null,"y","0");e.setAttributeNS(null,"width","1px");e.setAttributeNS(null,"height","1px");e.setAttributeNS(null,"fill",this.current.fillColor);this._ensureTransformGroup().appendChild(e)}paintImageXObject(e){const t=e.startsWith("g_")?this.commonObjs.get(e):this.objs.get(e);t?this.paintInlineImageXObject(t):(0,s.warn)(`Dependent image with object ID ${e} is not ready yet`)}paintInlineImageXObject(e,t){const r=e.width,s=e.height,n=h(e,this.forceDataSchema,!!t),a=this.svgFactory.createElement("svg:rect");a.setAttributeNS(null,"x","0");a.setAttributeNS(null,"y","0");a.setAttributeNS(null,"width",pf(r));a.setAttributeNS(null,"height",pf(s));this.current.element=a;this.clip("nonzero");const i=this.svgFactory.createElement("svg:image");i.setAttributeNS(o,"xlink:href",n);i.setAttributeNS(null,"x","0");i.setAttributeNS(null,"y",pf(-s));i.setAttributeNS(null,"width",pf(r)+"px");i.setAttributeNS(null,"height",pf(s)+"px");i.setAttributeNS(null,"transform",`scale(${pf(1/r)} ${pf(-1/s)})`);t?t.appendChild(i):this._ensureTransformGroup().appendChild(i)}paintImageMaskXObject(e){const t=this.current,r=e.width,s=e.height,n=t.fillColor;t.maskId="mask"+u++;const a=this.svgFactory.createElement("svg:mask");a.setAttributeNS(null,"id",t.maskId);const i=this.svgFactory.createElement("svg:rect");i.setAttributeNS(null,"x","0");i.setAttributeNS(null,"y","0");i.setAttributeNS(null,"width",pf(r));i.setAttributeNS(null,"height",pf(s));i.setAttributeNS(null,"fill",n);i.setAttributeNS(null,"mask",`url(#${t.maskId})`);this.defs.appendChild(a);this._ensureTransformGroup().appendChild(i);this.paintInlineImageXObject(e,a)}paintFormXObjectBegin(e,t){Array.isArray(e)&&6===e.length&&this.transform(e[0],e[1],e[2],e[3],e[4],e[5]);if(t){const e=t[2]-t[0],r=t[3]-t[1],s=this.svgFactory.createElement("svg:rect");s.setAttributeNS(null,"x",t[0]);s.setAttributeNS(null,"y",t[1]);s.setAttributeNS(null,"width",pf(e));s.setAttributeNS(null,"height",pf(r));this.current.element=s;this.clip("nonzero");this.endPath()}}paintFormXObjectEnd(){}_initialize(e){const t=this.svgFactory.create(e.width,e.height),r=this.svgFactory.createElement("svg:defs");t.appendChild(r);this.defs=r;const s=this.svgFactory.createElement("svg:g");s.setAttributeNS(null,"transform",pm(e.transform));t.appendChild(s);this.svg=s;return t}_ensureClipGroup(){if(!this.current.clipGroup){const e=this.svgFactory.createElement("svg:g");e.setAttributeNS(null,"clip-path",this.current.activeClipUrl);this.svg.appendChild(e);this.current.clipGroup=e}return this.current.clipGroup}_ensureTransformGroup(){if(!this.tgrp){this.tgrp=this.svgFactory.createElement("svg:g");this.tgrp.setAttributeNS(null,"transform",pm(this.transformMatrix));this.current.activeClipUrl?this._ensureClipGroup().appendChild(this.tgrp):this.svg.appendChild(this.tgrp)}return this.tgrp}}}},(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});t.XfaLayer=void 0;t.XfaLayer=class XfaLayer{static setupStorage(e,t,r,s,n){const a=s.getValue(t,{value:null});switch(r.name){case"textarea":null!==a.value&&(e.textContent=a.value);if("print"===n)break;e.addEventListener("input",(e=>{s.setValue(t,{value:e.target.value})}));break;case"input":if("radio"===r.attributes.type||"checkbox"===r.attributes.type){a.value===r.attributes.xfaOn&&e.setAttribute("checked",!0);if("print"===n)break;e.addEventListener("change",(e=>{s.setValue(t,{value:e.target.getAttribute("xfaOn")})}))}else{null!==a.value&&e.setAttribute("value",a.value);if("print"===n)break;e.addEventListener("input",(e=>{s.setValue(t,{value:e.target.value})}))}break;case"select":if(null!==a.value)for(const e of r.children)e.attributes.value===a.value&&(e.attributes.selected=!0);e.addEventListener("input",(e=>{const r=e.target.options,n=-1===r.selectedIndex?"":r[r.selectedIndex].value;s.setValue(t,{value:n})}))}}static setAttributes(e,t,r,s){const{attributes:n}=t;"radio"===n.type&&(n.name=`${n.name}-${s}`);for(const[t,r]of Object.entries(n))null!=r&&"dataId"!==t&&("style"!==t?"textContent"===t?e.textContent=r:"class"===t?e.setAttribute(t,r.join(" ")):e.setAttribute(t,r):Object.assign(e.style,r));r&&n.dataId&&this.setupStorage(e,n.dataId,t,r)}static render(e){const t=e.annotationStorage,r=e.xfa,s=e.intent||"display",n=document.createElement(r.name);r.attributes&&this.setAttributes(n,r);const a=[[r,-1,n]],i=e.div;i.appendChild(n);const o=`matrix(${e.viewport.transform.join(",")})`;i.style.transform=o;i.setAttribute("class","xfaLayer xfaFont");for(;a.length>0;){const[e,r,n]=a[a.length-1];if(r+1===e.children.length){a.pop();continue}const i=e.children[++a[a.length-1][1]];if(null===i)continue;const{name:o}=i;if("#text"===o){n.appendChild(document.createTextNode(i.value));continue}let l;l=i?.attributes?.xmlns?document.createElementNS(i.attributes.xmlns,o):document.createElement(o);n.appendChild(l);i.attributes&&this.setAttributes(l,i,t,s);i.children&&i.children.length>0?a.push([i,-1,l]):i.value&&l.appendChild(document.createTextNode(i.value))}for(const e of i.querySelectorAll(".xfaNonInteractive input, .xfaNonInteractive textarea"))e.setAttribute("readOnly",!0)}static update(e){const t=`matrix(${e.viewport.transform.join(",")})`;e.div.style.transform=t;e.div.hidden=!1}}},(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});t.PDFNodeStream=void 0;var s=r(2),n=r(23);const a=require("fs"),i=require("http"),o=require("https"),l=require("url"),c=/^file:\/\/\/[a-zA-Z]:\//;t.PDFNodeStream=class PDFNodeStream{constructor(e){this.source=e;this.url=function parseUrl(e){const t=l.parse(e);if("file:"===t.protocol||t.host)return t;if(/^[a-z]:[/\\]/i.test(e))return l.parse(`file:///${e}`);t.host||(t.protocol="file:");return t}(e.url);this.isHttp="http:"===this.url.protocol||"https:"===this.url.protocol;this.isFsUrl="file:"===this.url.protocol;this.httpHeaders=this.isHttp&&e.httpHeaders||{};this._fullRequestReader=null;this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){(0,s.assert)(!this._fullRequestReader,"PDFNodeStream.getFullReader can only be called once.");this._fullRequestReader=this.isFsUrl?new PDFNodeStreamFsFullReader(this):new PDFNodeStreamFullReader(this);return this._fullRequestReader}getRangeReader(e,t){if(t<=this._progressiveDataLength)return null;const r=this.isFsUrl?new PDFNodeStreamFsRangeReader(this,e,t):new PDFNodeStreamRangeReader(this,e,t);this._rangeRequestReaders.push(r);return r}cancelAllRequests(e){this._fullRequestReader&&this._fullRequestReader.cancel(e);for(const t of this._rangeRequestReaders.slice(0))t.cancel(e)}};class BaseFullReader{constructor(e){this._url=e.url;this._done=!1;this._storedError=null;this.onProgress=null;const t=e.source;this._contentLength=t.length;this._loaded=0;this._filename=null;this._disableRange=t.disableRange||!1;this._rangeChunkSize=t.rangeChunkSize;this._rangeChunkSize||this._disableRange||(this._disableRange=!0);this._isStreamingSupported=!t.disableStream;this._isRangeSupported=!t.disableRange;this._readableStream=null;this._readCapability=(0,s.createPromiseCapability)();this._headersCapability=(0,s.createPromiseCapability)()}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;if(this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const e=this._readableStream.read();if(null===e){this._readCapability=(0,s.createPromiseCapability)();return this.read()}this._loaded+=e.length;this.onProgress&&this.onProgress({loaded:this._loaded,total:this._contentLength});return{value:new Uint8Array(e).buffer,done:!1}}cancel(e){this._readableStream?this._readableStream.destroy(e):this._error(e)}_error(e){this._storedError=e;this._readCapability.resolve()}_setReadableStream(e){this._readableStream=e;e.on("readable",(()=>{this._readCapability.resolve()}));e.on("end",(()=>{e.destroy();this._done=!0;this._readCapability.resolve()}));e.on("error",(e=>{this._error(e)}));!this._isStreamingSupported&&this._isRangeSupported&&this._error(new s.AbortException("streaming is disabled"));this._storedError&&this._readableStream.destroy(this._storedError)}}class BaseRangeReader{constructor(e){this._url=e.url;this._done=!1;this._storedError=null;this.onProgress=null;this._loaded=0;this._readableStream=null;this._readCapability=(0,s.createPromiseCapability)();const t=e.source;this._isStreamingSupported=!t.disableStream}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;if(this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const e=this._readableStream.read();if(null===e){this._readCapability=(0,s.createPromiseCapability)();return this.read()}this._loaded+=e.length;this.onProgress&&this.onProgress({loaded:this._loaded});return{value:new Uint8Array(e).buffer,done:!1}}cancel(e){this._readableStream?this._readableStream.destroy(e):this._error(e)}_error(e){this._storedError=e;this._readCapability.resolve()}_setReadableStream(e){this._readableStream=e;e.on("readable",(()=>{this._readCapability.resolve()}));e.on("end",(()=>{e.destroy();this._done=!0;this._readCapability.resolve()}));e.on("error",(e=>{this._error(e)}));this._storedError&&this._readableStream.destroy(this._storedError)}}function createRequestOptions(e,t){return{protocol:e.protocol,auth:e.auth,host:e.hostname,port:e.port,path:e.path,method:"GET",headers:t}}class PDFNodeStreamFullReader extends BaseFullReader{constructor(e){super(e);const handleResponse=t=>{if(404===t.statusCode){const e=new s.MissingPDFException(`Missing PDF "${this._url}".`);this._storedError=e;this._headersCapability.reject(e);return}this._headersCapability.resolve();this._setReadableStream(t);const getResponseHeader=e=>this._readableStream.headers[e.toLowerCase()],{allowRangeRequests:r,suggestedLength:a}=(0,n.validateRangeRequestCapabilities)({getResponseHeader:getResponseHeader,isHttp:e.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=r;this._contentLength=a||this._contentLength;this._filename=(0,n.extractFilenameFromHeader)(getResponseHeader)};this._request=null;"http:"===this._url.protocol?this._request=i.request(createRequestOptions(this._url,e.httpHeaders),handleResponse):this._request=o.request(createRequestOptions(this._url,e.httpHeaders),handleResponse);this._request.on("error",(e=>{this._storedError=e;this._headersCapability.reject(e)}));this._request.end()}}class PDFNodeStreamRangeReader extends BaseRangeReader{constructor(e,t,r){super(e);this._httpHeaders={};for(const t in e.httpHeaders){const r=e.httpHeaders[t];void 0!==r&&(this._httpHeaders[t]=r)}this._httpHeaders.Range=`bytes=${t}-${r-1}`;const handleResponse=e=>{if(404!==e.statusCode)this._setReadableStream(e);else{const e=new s.MissingPDFException(`Missing PDF "${this._url}".`);this._storedError=e}};this._request=null;"http:"===this._url.protocol?this._request=i.request(createRequestOptions(this._url,this._httpHeaders),handleResponse):this._request=o.request(createRequestOptions(this._url,this._httpHeaders),handleResponse);this._request.on("error",(e=>{this._storedError=e}));this._request.end()}}class PDFNodeStreamFsFullReader extends BaseFullReader{constructor(e){super(e);let t=decodeURIComponent(this._url.path);c.test(this._url.href)&&(t=t.replace(/^\//,""));a.lstat(t,((e,r)=>{if(e){"ENOENT"===e.code&&(e=new s.MissingPDFException(`Missing PDF "${t}".`));this._storedError=e;this._headersCapability.reject(e)}else{this._contentLength=r.size;this._setReadableStream(a.createReadStream(t));this._headersCapability.resolve()}}))}}class PDFNodeStreamFsRangeReader extends BaseRangeReader{constructor(e,t,r){super(e);let s=decodeURIComponent(this._url.path);c.test(this._url.href)&&(s=s.replace(/^\//,""));this._setReadableStream(a.createReadStream(s,{start:t,end:r-1}))}}},(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});t.createResponseStatusError=function createResponseStatusError(e,t){if(404===e||0===e&&t.startsWith("file:"))return new s.MissingPDFException('Missing PDF "'+t+'".');return new s.UnexpectedResponseException(`Unexpected server response (${e}) while retrieving PDF "${t}".`,e)};t.extractFilenameFromHeader=function extractFilenameFromHeader(e){const t=e("Content-Disposition");if(t){let e=(0,n.getFilenameFromContentDispositionHeader)(t);if(e.includes("%"))try{e=decodeURIComponent(e)}catch(e){}if((0,a.isPdfFile)(e))return e}return null};t.validateRangeRequestCapabilities=function validateRangeRequestCapabilities({getResponseHeader:e,isHttp:t,rangeChunkSize:r,disableRange:n}){(0,s.assert)(r>0,"Range chunk size must be larger than zero");const a={allowRangeRequests:!1,suggestedLength:void 0},i=parseInt(e("Content-Length"),10);if(!Number.isInteger(i))return a;a.suggestedLength=i;if(i<=2*r)return a;if(n||!t)return a;if("bytes"!==e("Accept-Ranges"))return a;if("identity"!==(e("Content-Encoding")||"identity"))return a;a.allowRangeRequests=!0;return a};t.validateResponseStatus=function validateResponseStatus(e){return 200===e||206===e};var s=r(2),n=r(24),a=r(1)},(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});t.getFilenameFromContentDispositionHeader=function getFilenameFromContentDispositionHeader(e){let t=!0,r=toParamRegExp("filename\\*","i").exec(e);if(r){r=r[1];let e=rfc2616unquote(r);e=unescape(e);e=rfc5987decode(e);e=rfc2047decode(e);return fixupEncoding(e)}r=function rfc2231getparam(e){const t=[];let r;const s=toParamRegExp("filename\\*((?!0\\d)\\d+)(\\*?)","ig");for(;null!==(r=s.exec(e));){let[,e,s,n]=r;e=parseInt(e,10);if(e in t){if(0===e)break}else t[e]=[s,n]}const n=[];for(let e=0;e<t.length&&e in t;++e){let[r,s]=t[e];s=rfc2616unquote(s);if(r){s=unescape(s);0===e&&(s=rfc5987decode(s))}n.push(s)}return n.join("")}(e);if(r){return fixupEncoding(rfc2047decode(r))}r=toParamRegExp("filename","i").exec(e);if(r){r=r[1];let e=rfc2616unquote(r);e=rfc2047decode(e);return fixupEncoding(e)}function toParamRegExp(e,t){return new RegExp("(?:^|;)\\s*"+e+'\\s*=\\s*([^";\\s][^;\\s]*|"(?:[^"\\\\]|\\\\"?)+"?)',t)}function textdecode(e,r){if(e){if(!/^[\x00-\xFF]+$/.test(r))return r;try{const n=new TextDecoder(e,{fatal:!0}),a=(0,s.stringToBytes)(r);r=n.decode(a);t=!1}catch(s){if(/^utf-?8$/i.test(e))try{r=decodeURIComponent(escape(r));t=!1}catch(e){}}}return r}function fixupEncoding(e){if(t&&/[\x80-\xff]/.test(e)){e=textdecode("utf-8",e);t&&(e=textdecode("iso-8859-1",e))}return e}function rfc2616unquote(e){if(e.startsWith('"')){const t=e.slice(1).split('\\"');for(let e=0;e<t.length;++e){const r=t[e].indexOf('"');if(-1!==r){t[e]=t[e].slice(0,r);t.length=e+1}t[e]=t[e].replace(/\\(.)/g,"$1")}e=t.join('"')}return e}function rfc5987decode(e){const t=e.indexOf("'");if(-1===t)return e;return textdecode(e.slice(0,t),e.slice(t+1).replace(/^[^']*'/,""))}function rfc2047decode(e){return!e.startsWith("=?")||/[\x00-\x19\x80-\xff]/.test(e)?e:e.replace(/=\?([\w-]*)\?([QqBb])\?((?:[^?]|\?(?!=))*)\?=/g,(function(e,t,r,s){if("q"===r||"Q"===r)return textdecode(t,s=(s=s.replace(/_/g," ")).replace(/=([0-9a-fA-F]{2})/g,(function(e,t){return String.fromCharCode(parseInt(t,16))})));try{s=atob(s)}catch(e){}return textdecode(t,s)}))}return""};var s=r(2)},(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});t.PDFNetworkStream=void 0;var s=r(2),n=r(23);class NetworkManager{constructor(e,t){this.url=e;t=t||{};this.isHttp=/^https?:/i.test(e);this.httpHeaders=this.isHttp&&t.httpHeaders||{};this.withCredentials=t.withCredentials||!1;this.getXhr=t.getXhr||function NetworkManager_getXhr(){return new XMLHttpRequest};this.currXhrId=0;this.pendingRequests=Object.create(null)}requestRange(e,t,r){const s={begin:e,end:t};for(const e in r)s[e]=r[e];return this.request(s)}requestFull(e){return this.request(e)}request(e){const t=this.getXhr(),r=this.currXhrId++,s=this.pendingRequests[r]={xhr:t};t.open("GET",this.url);t.withCredentials=this.withCredentials;for(const e in this.httpHeaders){const r=this.httpHeaders[e];void 0!==r&&t.setRequestHeader(e,r)}if(this.isHttp&&"begin"in e&&"end"in e){t.setRequestHeader("Range",`bytes=${e.begin}-${e.end-1}`);s.expectedStatus=206}else s.expectedStatus=200;t.responseType="arraybuffer";e.onError&&(t.onerror=function(r){e.onError(t.status)});t.onreadystatechange=this.onStateChange.bind(this,r);t.onprogress=this.onProgress.bind(this,r);s.onHeadersReceived=e.onHeadersReceived;s.onDone=e.onDone;s.onError=e.onError;s.onProgress=e.onProgress;t.send(null);return r}onProgress(e,t){const r=this.pendingRequests[e];r&&r.onProgress&&r.onProgress(t)}onStateChange(e,t){const r=this.pendingRequests[e];if(!r)return;const n=r.xhr;if(n.readyState>=2&&r.onHeadersReceived){r.onHeadersReceived();delete r.onHeadersReceived}if(4!==n.readyState)return;if(!(e in this.pendingRequests))return;delete this.pendingRequests[e];if(0===n.status&&this.isHttp){r.onError&&r.onError(n.status);return}const a=n.status||200;if(!(200===a&&206===r.expectedStatus)&&a!==r.expectedStatus){r.onError&&r.onError(n.status);return}const i=function getArrayBuffer(e){const t=e.response;return"string"!=typeof t?t:(0,s.stringToBytes)(t).buffer}(n);if(206===a){const e=n.getResponseHeader("Content-Range"),t=/bytes (\d+)-(\d+)\/(\d+)/.exec(e);r.onDone({begin:parseInt(t[1],10),chunk:i})}else i?r.onDone({begin:0,chunk:i}):r.onError&&r.onError(n.status)}getRequestXhr(e){return this.pendingRequests[e].xhr}isPendingRequest(e){return e in this.pendingRequests}abortRequest(e){const t=this.pendingRequests[e].xhr;delete this.pendingRequests[e];t.abort()}}t.PDFNetworkStream=class PDFNetworkStream{constructor(e){this._source=e;this._manager=new NetworkManager(e.url,{httpHeaders:e.httpHeaders,withCredentials:e.withCredentials});this._rangeChunkSize=e.rangeChunkSize;this._fullRequestReader=null;this._rangeRequestReaders=[]}_onRangeRequestReaderClosed(e){const t=this._rangeRequestReaders.indexOf(e);t>=0&&this._rangeRequestReaders.splice(t,1)}getFullReader(){(0,s.assert)(!this._fullRequestReader,"PDFNetworkStream.getFullReader can only be called once.");this._fullRequestReader=new PDFNetworkStreamFullRequestReader(this._manager,this._source);return this._fullRequestReader}getRangeReader(e,t){const r=new PDFNetworkStreamRangeRequestReader(this._manager,e,t);r.onClosed=this._onRangeRequestReaderClosed.bind(this);this._rangeRequestReaders.push(r);return r}cancelAllRequests(e){this._fullRequestReader&&this._fullRequestReader.cancel(e);for(const t of this._rangeRequestReaders.slice(0))t.cancel(e)}};class PDFNetworkStreamFullRequestReader{constructor(e,t){this._manager=e;const r={onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=t.url;this._fullRequestId=e.requestFull(r);this._headersReceivedCapability=(0,s.createPromiseCapability)();this._disableRange=t.disableRange||!1;this._contentLength=t.length;this._rangeChunkSize=t.rangeChunkSize;this._rangeChunkSize||this._disableRange||(this._disableRange=!0);this._isStreamingSupported=!1;this._isRangeSupported=!1;this._cachedChunks=[];this._requests=[];this._done=!1;this._storedError=void 0;this._filename=null;this.onProgress=null}_onHeadersReceived(){const e=this._fullRequestId,t=this._manager.getRequestXhr(e),getResponseHeader=e=>t.getResponseHeader(e),{allowRangeRequests:r,suggestedLength:s}=(0,n.validateRangeRequestCapabilities)({getResponseHeader:getResponseHeader,isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});r&&(this._isRangeSupported=!0);this._contentLength=s||this._contentLength;this._filename=(0,n.extractFilenameFromHeader)(getResponseHeader);this._isRangeSupported&&this._manager.abortRequest(e);this._headersReceivedCapability.resolve()}_onDone(e){if(e)if(this._requests.length>0){this._requests.shift().resolve({value:e.chunk,done:!1})}else this._cachedChunks.push(e.chunk);this._done=!0;if(!(this._cachedChunks.length>0)){for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0}}_onError(e){const t=this._url,r=(0,n.createResponseStatusError)(e,t);this._storedError=r;this._headersReceivedCapability.reject(r);for(const e of this._requests)e.reject(r);this._requests.length=0;this._cachedChunks.length=0}_onProgress(e){this.onProgress&&this.onProgress({loaded:e.loaded,total:e.lengthComputable?e.total:this._contentLength})}get filename(){return this._filename}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}get contentLength(){return this._contentLength}get headersReady(){return this._headersReceivedCapability.promise}async read(){if(this._storedError)throw this._storedError;if(this._cachedChunks.length>0){return{value:this._cachedChunks.shift(),done:!1}}if(this._done)return{value:void 0,done:!0};const e=(0,s.createPromiseCapability)();this._requests.push(e);return e.promise}cancel(e){this._done=!0;this._headersReceivedCapability.reject(e);for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0;this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId);this._fullRequestReader=null}}class PDFNetworkStreamRangeRequestReader{constructor(e,t,r){this._manager=e;const s={onDone:this._onDone.bind(this),onProgress:this._onProgress.bind(this)};this._requestId=e.requestRange(t,r,s);this._requests=[];this._queuedChunk=null;this._done=!1;this.onProgress=null;this.onClosed=null}_close(){this.onClosed&&this.onClosed(this)}_onDone(e){const t=e.chunk;if(this._requests.length>0){this._requests.shift().resolve({value:t,done:!1})}else this._queuedChunk=t;this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0;this._close()}_onProgress(e){!this.isStreamingSupported&&this.onProgress&&this.onProgress({loaded:e.loaded})}get isStreamingSupported(){return!1}async read(){if(null!==this._queuedChunk){const e=this._queuedChunk;this._queuedChunk=null;return{value:e,done:!1}}if(this._done)return{value:void 0,done:!0};const e=(0,s.createPromiseCapability)();this._requests.push(e);return e.promise}cancel(e){this._done=!0;for(const e of this._requests)e.resolve({value:void 0,done:!0});this._requests.length=0;this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId);this._close()}}},(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});t.PDFFetchStream=void 0;var s=r(2),n=r(23);function createFetchOptions(e,t,r){return{method:"GET",headers:e,signal:r?.signal,mode:"cors",credentials:t?"include":"same-origin",redirect:"follow"}}function createHeaders(e){const t=new Headers;for(const r in e){const s=e[r];void 0!==s&&t.append(r,s)}return t}t.PDFFetchStream=class PDFFetchStream{constructor(e){this.source=e;this.isHttp=/^https?:/i.test(e.url);this.httpHeaders=this.isHttp&&e.httpHeaders||{};this._fullRequestReader=null;this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){(0,s.assert)(!this._fullRequestReader,"PDFFetchStream.getFullReader can only be called once.");this._fullRequestReader=new PDFFetchStreamReader(this);return this._fullRequestReader}getRangeReader(e,t){if(t<=this._progressiveDataLength)return null;const r=new PDFFetchStreamRangeReader(this,e,t);this._rangeRequestReaders.push(r);return r}cancelAllRequests(e){this._fullRequestReader&&this._fullRequestReader.cancel(e);for(const t of this._rangeRequestReaders.slice(0))t.cancel(e)}};class PDFFetchStreamReader{constructor(e){this._stream=e;this._reader=null;this._loaded=0;this._filename=null;const t=e.source;this._withCredentials=t.withCredentials||!1;this._contentLength=t.length;this._headersCapability=(0,s.createPromiseCapability)();this._disableRange=t.disableRange||!1;this._rangeChunkSize=t.rangeChunkSize;this._rangeChunkSize||this._disableRange||(this._disableRange=!0);"undefined"!=typeof AbortController&&(this._abortController=new AbortController);this._isStreamingSupported=!t.disableStream;this._isRangeSupported=!t.disableRange;this._headers=createHeaders(this._stream.httpHeaders);const r=t.url;fetch(r,createFetchOptions(this._headers,this._withCredentials,this._abortController)).then((e=>{if(!(0,n.validateResponseStatus)(e.status))throw(0,n.createResponseStatusError)(e.status,r);this._reader=e.body.getReader();this._headersCapability.resolve();const getResponseHeader=t=>e.headers.get(t),{allowRangeRequests:t,suggestedLength:a}=(0,n.validateRangeRequestCapabilities)({getResponseHeader:getResponseHeader,isHttp:this._stream.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=t;this._contentLength=a||this._contentLength;this._filename=(0,n.extractFilenameFromHeader)(getResponseHeader);!this._isStreamingSupported&&this._isRangeSupported&&this.cancel(new s.AbortException("Streaming is disabled."))})).catch(this._headersCapability.reject);this.onProgress=null}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._headersCapability.promise;const{value:e,done:t}=await this._reader.read();if(t)return{value:e,done:t};this._loaded+=e.byteLength;this.onProgress&&this.onProgress({loaded:this._loaded,total:this._contentLength});return{value:new Uint8Array(e).buffer,done:!1}}cancel(e){this._reader&&this._reader.cancel(e);this._abortController&&this._abortController.abort()}}class PDFFetchStreamRangeReader{constructor(e,t,r){this._stream=e;this._reader=null;this._loaded=0;const a=e.source;this._withCredentials=a.withCredentials||!1;this._readCapability=(0,s.createPromiseCapability)();this._isStreamingSupported=!a.disableStream;"undefined"!=typeof AbortController&&(this._abortController=new AbortController);this._headers=createHeaders(this._stream.httpHeaders);this._headers.append("Range",`bytes=${t}-${r-1}`);const i=a.url;fetch(i,createFetchOptions(this._headers,this._withCredentials,this._abortController)).then((e=>{if(!(0,n.validateResponseStatus)(e.status))throw(0,n.createResponseStatusError)(e.status,i);this._readCapability.resolve();this._reader=e.body.getReader()})).catch((e=>{if("AbortError"!==e?.name)throw e}));this.onProgress=null}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;const{value:e,done:t}=await this._reader.read();if(t)return{value:e,done:t};this._loaded+=e.byteLength;this.onProgress&&this.onProgress({loaded:this._loaded});return{value:new Uint8Array(e).buffer,done:!1}}cancel(e){this._reader&&this._reader.cancel(e);this._abortController&&this._abortController.abort()}}}],__webpack_module_cache__={};function __w_pdfjs_require__(e){var t=__webpack_module_cache__[e];if(void 0!==t)return t.exports;var r=__webpack_module_cache__[e]={exports:{}};__webpack_modules__[e](r,r.exports,__w_pdfjs_require__);return r.exports}var __webpack_exports__={};(()=>{var e=__webpack_exports__;Object.defineProperty(e,"__esModule",{value:!0});Object.defineProperty(e,"addLinkAttributes",{enumerable:!0,get:function(){return t.addLinkAttributes}});Object.defineProperty(e,"getFilenameFromUrl",{enumerable:!0,get:function(){return t.getFilenameFromUrl}});Object.defineProperty(e,"getPdfFilenameFromUrl",{enumerable:!0,get:function(){return t.getPdfFilenameFromUrl}});Object.defineProperty(e,"getXfaPageViewport",{enumerable:!0,get:function(){return t.getXfaPageViewport}});Object.defineProperty(e,"isPdfFile",{enumerable:!0,get:function(){return t.isPdfFile}});Object.defineProperty(e,"LinkTarget",{enumerable:!0,get:function(){return t.LinkTarget}});Object.defineProperty(e,"loadScript",{enumerable:!0,get:function(){return t.loadScript}});Object.defineProperty(e,"PDFDateString",{enumerable:!0,get:function(){return t.PDFDateString}});Object.defineProperty(e,"RenderingCancelledException",{enumerable:!0,get:function(){return t.RenderingCancelledException}});Object.defineProperty(e,"build",{enumerable:!0,get:function(){return r.build}});Object.defineProperty(e,"getDocument",{enumerable:!0,get:function(){return r.getDocument}});Object.defineProperty(e,"LoopbackPort",{enumerable:!0,get:function(){return r.LoopbackPort}});Object.defineProperty(e,"PDFDataRangeTransport",{enumerable:!0,get:function(){return r.PDFDataRangeTransport}});Object.defineProperty(e,"PDFWorker",{enumerable:!0,get:function(){return r.PDFWorker}});Object.defineProperty(e,"version",{enumerable:!0,get:function(){return r.version}});Object.defineProperty(e,"CMapCompressionType",{enumerable:!0,get:function(){return s.CMapCompressionType}});Object.defineProperty(e,"createObjectURL",{enumerable:!0,get:function(){return s.createObjectURL}});Object.defineProperty(e,"createPromiseCapability",{enumerable:!0,get:function(){return s.createPromiseCapability}});Object.defineProperty(e,"createValidAbsoluteUrl",{enumerable:!0,get:function(){return s.createValidAbsoluteUrl}});Object.defineProperty(e,"InvalidPDFException",{enumerable:!0,get:function(){return s.InvalidPDFException}});Object.defineProperty(e,"MissingPDFException",{enumerable:!0,get:function(){return s.MissingPDFException}});Object.defineProperty(e,"OPS",{enumerable:!0,get:function(){return s.OPS}});Object.defineProperty(e,"PasswordResponses",{enumerable:!0,get:function(){return s.PasswordResponses}});Object.defineProperty(e,"PermissionFlag",{enumerable:!0,get:function(){return s.PermissionFlag}});Object.defineProperty(e,"removeNullCharacters",{enumerable:!0,get:function(){return s.removeNullCharacters}});Object.defineProperty(e,"shadow",{enumerable:!0,get:function(){return s.shadow}});Object.defineProperty(e,"UnexpectedResponseException",{enumerable:!0,get:function(){return s.UnexpectedResponseException}});Object.defineProperty(e,"UNSUPPORTED_FEATURES",{enumerable:!0,get:function(){return s.UNSUPPORTED_FEATURES}});Object.defineProperty(e,"Util",{enumerable:!0,get:function(){return s.Util}});Object.defineProperty(e,"VerbosityLevel",{enumerable:!0,get:function(){return s.VerbosityLevel}});Object.defineProperty(e,"AnnotationLayer",{enumerable:!0,get:function(){return n.AnnotationLayer}});Object.defineProperty(e,"GlobalWorkerOptions",{enumerable:!0,get:function(){return a.GlobalWorkerOptions}});Object.defineProperty(e,"renderTextLayer",{enumerable:!0,get:function(){return o.renderTextLayer}});Object.defineProperty(e,"SVGGraphics",{enumerable:!0,get:function(){return l.SVGGraphics}});Object.defineProperty(e,"XfaLayer",{enumerable:!0,get:function(){return c.XfaLayer}});var t=__w_pdfjs_require__(1),r=__w_pdfjs_require__(6),s=__w_pdfjs_require__(2),n=__w_pdfjs_require__(17),a=__w_pdfjs_require__(12),i=__w_pdfjs_require__(4),o=__w_pdfjs_require__(19),l=__w_pdfjs_require__(20),c=__w_pdfjs_require__(21);if(i.isNodeJS){const{PDFNodeStream:e}=__w_pdfjs_require__(22);(0,r.setPDFNetworkStreamFactory)((t=>new e(t)))}else{const{PDFNetworkStream:e}=__w_pdfjs_require__(25),{PDFFetchStream:s}=__w_pdfjs_require__(26);(0,r.setPDFNetworkStreamFactory)((r=>(0,t.isValidFetchUrl)(r.url)?new s(r):new e(r)))}})();return __webpack_exports__})()}));
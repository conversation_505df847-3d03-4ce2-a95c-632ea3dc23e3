// 导入必要的 Electron 和 Node.js 模块
const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');
const fs = require('fs');

// 声明主窗口变量
let mainWindow;

// 函数：创建应用程序窗口
function createWindow() {
  // 创建一个新的浏览器窗口
  mainWindow = new BrowserWindow({
    width: 800,
    height: 600,
    fullscreen: false, // 默认以窗口模式启动
    autoHideMenuBar: true, // 自动隐藏菜单栏
    webPreferences: {
      preload: path.join(__dirname, 'renderer.js'), // 预加载渲染器脚本
      contextIsolation: true,
      nodeIntegration: false
    }
  });

  // 加载 index.html 文件
  mainWindow.loadFile('index.html');

  // 当窗口关闭时，取消对 mainWindow 的引用
  mainWindow.on('closed', function () {
    mainWindow = null;
  });
}

// 函数：读取配置文件并获取 PDF 列表
function getPdfFiles() {
  try {
    // 读取配置文件
    const configPath = path.join(__dirname, 'config.json');
    const config = JSON.parse(fs.readFileSync(configPath, 'utf-8'));
    const pdfFolderPath = config.pdfFolderPath;

    // 检查文件夹是否存在
    if (!fs.existsSync(pdfFolderPath)) {
      console.error('配置的 PDF 文件夹路径不存在:', pdfFolderPath);
      return [];
    }

    // 读取文件夹内容并过滤出 PDF 文件
    const files = fs.readdirSync(pdfFolderPath);
    const pdfs = files
      .filter(file => path.extname(file).toLowerCase() === '.pdf')
      .map(file => path.join(pdfFolderPath, file));
    console.log(`在文件夹 "${pdfFolderPath}" 中找到 ${pdfs.length} 个 PDF 文件。`);
    return pdfs;
  } catch (error) {
    console.error('读取配置文件或 PDF 文件列表时出错:', error);
    return [];
  }
}

// Electron 应用程序准备就绪后执行
app.on('ready', () => {
  createWindow();

  // 应用程序启动后立即发送 PDF 列表
  const pdfFiles = getPdfFiles();
  if (pdfFiles.length > 0) {
    mainWindow.webContents.on('did-finish-load', () => {
     console.log('将以下 PDF 文件列表发送到渲染器进程:', pdfFiles);
     mainWindow.webContents.send('pdf-files', pdfFiles);
    });
  } else {
    console.log('在指定文件夹中未找到 PDF 文件。');
  }
});

// 当所有窗口都关闭时退出应用程序
app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// 在 macOS 上，当点击 dock 图标且没有其他窗口打开时，重新创建一个窗口
app.on('activate', function () {
  if (mainWindow === null) {
    createWindow();
  }
});

// 监听来自渲染器的请求，以获取 PDF 文件列表
ipcMain.handle('get-pdf-files', (event) => {
  return getPdfFiles();
});
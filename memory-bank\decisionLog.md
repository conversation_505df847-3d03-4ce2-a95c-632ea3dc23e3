# Decision Log

This file records architectural and implementation decisions using a list format.
2025-06-30 10:55:57 - Log of updates made.

*
      
## Decision

*
      
## Rationale 

*

## Implementation Details

*

---
### Decision (Debug)
[2025-06-30 11:09:56] - Bug Fix Strategy: Improve user feedback for missing PDFs.

**Rationale:**
The original issue was a black screen when no PDFs were found. Instead of just a blank screen, providing a clear, user-friendly error message directly within the application window improves usability and helps the user self-diagnose the problem. This is better than relying on console logs, which the end-user might not see.

**Details:**
- Modified `renderer.js` to inject a detailed HTML message into the `pdf-container` when the incoming PDF file list is empty.
- Added styles to `styles.css` to ensure the new message is legible on the default black background.

---
### Decision (Debug)
[2025-06-30 11:13:35] - Bug Fix Strategy: Enhance logging and provide specific file error feedback.

**Rationale:**
The original issue was a silent failure (black screen) when a single PDF in the sequence failed to load. To improve diagnostics and user experience, two changes were made: 1) Added more detailed logging in both the main and renderer processes to trace the flow of PDF files. 2) Implemented a specific, user-facing error message in the UI that identifies which file failed to load and informs the user that the application will proceed to the next file. This prevents the application from getting stuck and provides clear feedback.

**Details:**
- Modified `main.js` to log the number of PDFs found and the list of files being sent to the renderer.
- Modified `renderer.js` to log which file it is attempting to load.
- The `catch` block in `renderer.js`'s `loadPdf` function was updated to inject an HTML error message into the `pdf-container` instead of only logging to the console.
- Updated `README.md` to include a "Troubleshooting" section explaining how to open the developer console.
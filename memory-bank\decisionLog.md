# Decision Log

This file records architectural and implementation decisions using a list format.
2025-06-30 10:55:57 - Log of updates made.

*
      
## Decision

*
      
## Rationale 

*

## Implementation Details

*

---
### Decision (Debug)
[2025-06-30 11:09:56] - Bug Fix Strategy: Improve user feedback for missing PDFs.

**Rationale:**
The original issue was a black screen when no PDFs were found. Instead of just a blank screen, providing a clear, user-friendly error message directly within the application window improves usability and helps the user self-diagnose the problem. This is better than relying on console logs, which the end-user might not see.

**Details:**
- Modified `renderer.js` to inject a detailed HTML message into the `pdf-container` when the incoming PDF file list is empty.
- Added styles to `styles.css` to ensure the new message is legible on the default black background.

---
### Decision (Debug)
[2025-06-30 11:13:35] - Bug Fix Strategy: Enhance logging and provide specific file error feedback.

**Rationale:**
The original issue was a silent failure (black screen) when a single PDF in the sequence failed to load. To improve diagnostics and user experience, two changes were made: 1) Added more detailed logging in both the main and renderer processes to trace the flow of PDF files. 2) Implemented a specific, user-facing error message in the UI that identifies which file failed to load and informs the user that the application will proceed to the next file. This prevents the application from getting stuck and provides clear feedback.

**Details:**
- Modified `main.js` to log the number of PDFs found and the list of files being sent to the renderer.
- Modified `renderer.js` to log which file it is attempting to load.
- The `catch` block in `renderer.js`'s `loadPdf` function was updated to inject an HTML error message into the `pdf-container` instead of only logging to the console.
- Updated `README.md` to include a "Troubleshooting" section explaining how to open the developer console.

---
### Decision (Debug)
[2025-06-30 11:37:57] - [Bug Fix Strategy: 统一错误样式并优化文档]

**Rationale:**
为了解决用户反馈的错误信息在深色背景下不可见，以及新手用户对文件结构困惑的问题，决定采用统一的CSS类来标准化所有错误提示的视觉表现，并为`README.md`文件增加清晰的目录结构图。这能从根本上提升可用性和易用性。

**Details:**
*   **Affected components/files:**
    *   `styles.css`: 新增了 `.error-message` CSS 类，以确保错误信息在任何背景下都清晰可见。
    *   `renderer.js`: 修改了所有生成错误HTML的地方，将其包裹在 `<div class="error-message">` 中，以应用新的统一样式。
    *   `README.md`: 在手动设置部分增加了预格式化的目录结构图和额外的文字提醒，以减少用户配置错误的可能性。

---
### Decision (Debug)
[2025-06-30 11:44:51] - Bug Fix Strategy: Implement robust PDF.js loading and configurable smooth scrolling.

**Rationale:**
The application suffered from two core issues: 1) A race condition where `app.js` could execute before the `pdf.js` library was fully loaded. 2) A jarring "jump-to-next-page" behavior instead of smooth scrolling. To fix this, a polling mechanism was introduced to ensure the library is ready, and the scrolling logic was completely overhauled to be smooth, configurable, and intelligent (it only scrolls if the content is larger than the viewport).

**Details:**
*   **Affected components/files:**
    *   `app.js`: Completely rewritten to include a `initialize` function that polls for `pdfjsLib`. The `runApp` logic was updated to fetch configuration from the main process and implement a new `startScrolling` function that handles smooth scrolling based on canvas height versus container height.
    *   `config.json`: Added `scrollDelay` and `pageChangeDelay` parameters to allow users to customize the scrolling and page transition speeds.
    *   `main.js`: Refactored to provide a generic `getConfig()` IPC handler, making the entire configuration object available to the renderer process, rather than just the PDF file list.
    *   `renderer.js`: Updated the preload script to expose the new `getConfig` function to the `window.electronAPI`.

---
### Decision (Debug)
[2025-06-30 11:48:46] - [Bug Fix Strategy: Implement programmatic cache clearing on startup]

**Rationale:**
Persisting old error messages strongly indicated that Electron's caching mechanism was serving stale code or resources. Clearing the cache at each application launch is a definitive way to ensure that only the latest files are loaded, resolving the root cause of the issue.

**Details:**
- Modified `main.js` in the `createWindow` function.
- Replaced `mainWindow.loadFile('index.html');` with a promise chain that first calls `mainWindow.webContents.session.clearCache()` before loading the file.
- Added a `catch` block to ensure the application still attempts to load even if cache clearing fails.
- Updated `README.md` to inform users of this new behavior.

---
### Decision (Code)
[2025-06-30 12:15:00] - Refactor: Implement continuous smooth scrolling instead of page-by-page flipping.

**Rationale:**
Based on user feedback, the page-flipping effect was jarring. A continuous smooth scrolling experience is more modern and visually seamless for unattended display. This requires a significant refactoring to render all PDF pages into a single scrollable container rather than one page at a time.

**Details:**
*   **`config.json`**: Added `scrollSpeed` to allow users to control the scrolling velocity.
*   **`main.js`**: Modified the `get-pdf-files` IPC handler to return both the file list and the full configuration object, providing the renderer with all necessary parameters.
*   **`app.js`**:
    *   `loadPdf` was completely rewritten to loop through all pages of a PDF, render each to a separate `<canvas>`, and append them all to the `pdfContainer`.
    *   `startScrolling` was updated to use the `scrollSpeed` from the config and now checks for reaching the bottom of the entire `pdfContainer`.
    *   `nextPage` was renamed to `loadNextPdf` for clarity, and its logic was simplified to just load the next file in the list.
*   **`README.md`**: Updated the configuration section to document the new `scrollSpeed` option and clarify other related settings.

---
### Decision (Code)
[2025-06-30 12:20:15] - Refactor: Implement time-based vertical scrolling.

**Rationale:**
Based on user feedback, the previous scrolling mechanism was not intuitive. The direction was sometimes incorrect, and the speed control (`scrollSpeed`) was arbitrary and not based on a clear metric. This refactoring addresses these issues by:
1.  Enforcing a pure vertical scroll layout using CSS `flex-direction: column`.
2.  Replacing the abstract `scrollSpeed` with a more intuitive `secondsPerPage` configuration. This allows users to define how long it should take to scroll through one page of content, making the speed predictable and meaningful.
3.  The core scrolling logic in `app.js` was rewritten to calculate the precise scroll increment based on the page height and the desired time per page, ensuring accurate and smooth time-based scrolling.

**Details:**
*   **`styles.css`**: Added `flex-direction: column` to `#pdf-container` to ensure vertical stacking of pages.
*   **`config.json`**: Replaced `scrollSpeed` with `secondsPerPage`.
*   **`main.js`**: Added a default value for `secondsPerPage` to ensure backward compatibility and prevent errors.
*   **`app.js`**:
    *   `loadPdf` now captures the height of the first page.
    *   `startScrolling` was completely refactored to accept `pageHeight` and calculate a `scrollIncrement` based on `secondsPerPage` and the scroll interval, resulting in time-based scrolling.
*   **`README.md`**: Updated the configuration section to document the new `secondsPerPage` option.
- **2025-06-30 13:55:27**: Decided to use a local Electron binary zip file to bypass network download issues during the build process. This was necessary after multiple build failures due to corrupted downloads. The environment variable `ELECTRON_CACHE` was set to point to the local file path.

---
### Decision (Debug)
[2025-06-30 14:03:24] - Bug Fix Strategy: Enable external configuration for compiled application.

**Rationale:**
The application originally bundled the `config.json` file, making it impossible for users to edit settings after compilation. The fix involves modifying the configuration loading logic to prioritize an external `config.json` placed next to the executable. This provides users with a simple and effective way to customize the application's behavior without needing to rebuild it, while still providing a default internal configuration as a fallback.

**Details:**
- Modified `main.js` in the `getConfig` function.
- The logic now first checks for `config.json` in the same directory as the application executable (`app.getPath('exe')`).
- If the external file is found, it is loaded.
- If it is not found or fails to load, the application falls back to loading the `config.json` bundled inside the application's resources (`__dirname`).
---
**Decision:** Refactor scrolling logic to use `requestAnimationFrame`.
**Date:** 2025-06-30
**Rationale:** The previous implementation used `setInterval` for scrolling, which caused stuttering and an unsmooth user experience. `requestAnimationFrame` synchronizes the animation with the browser's repaint cycle, resulting in a much smoother and more performant scroll. This is a standard best practice for web animations.
**Impact:** Improved scrolling performance and visual smoothness for the PDF viewer.
- [2025-06-30 14:31:30] - DEPLOYMENT - Decided to build the project as a final step after frontend optimization.

---
### Decision
[2025-06-30 14:54:00] - Define High-Level Flutter Architecture for PDF Auto-Player.

**Rationale:**
To transition the project from an Electron-based desktop application to a cross-platform mobile/desktop application, a new architecture based on Flutter is required. This architecture leverages modern, robust Flutter plugins and state management practices to ensure scalability and maintainability. The chosen stack (`riverpod` for state management, `flutter_pdfview` for rendering) provides a solid foundation that mirrors the functionality of the original Electron app while adhering to Flutter best practices.

**Implementation Details:**
The architecture is documented in `memory-bank/systemPatterns.md`. Key aspects include:
*   **Technology Stack**: `flutter_pdfview`, `riverpod`, `path_provider`.
*   **Component Structure**: A widget tree centered around `HomePage`, `PdfPlayer`, and `PdfViewWrapper`.
*   **Data Flow**: A unidirectional data flow managed by Riverpod providers (`configProvider`, `pdfListProvider`, `playerStateProvider`) to handle configuration, file loading, and playback state.
*   **Services**: Separation of concerns with `ConfigService` and `PdfFileService` handling business logic.

---
### Decision (Debug)
[2025-06-30 15:24:20] - Bug Fix Strategy: Read `config.json` from the executable's directory.

**Rationale:**
The compiled Flutter application for Windows was not using the external `config.json` because the configuration was hardcoded. To allow users to configure the application after it has been compiled, the logic was changed to dynamically locate and read the `config.json` file from the same directory where the application's executable resides. This provides flexibility and aligns with standard practices for desktop application configuration.

**Details:**
- Modified `flutter_pdf_viewer/lib/services/config_service.dart`.
- Used `Platform.resolvedExecutable` to get the path of the running executable.
- Used the `path` package to extract the directory from the executable's path.
- The application now attempts to load `config.json` from this directory.
- If `config.json` is not found or an error occurs, it falls back to a default, hardcoded configuration to ensure the application can still run.
- Added the `path` package to `pubspec.yaml`.
---
### Decision
[2025-06-30 15:33:00] - Replace `flutter_pdfview` with `pdfx` for Windows Compatibility.

**Rationale:**
The existing `flutter_pdfview` plugin is not compatible with the Windows platform, which is a critical requirement for the project. This incompatibility prevents the application from being built and run on Windows. After evaluating alternatives, `pdfx` was chosen as the replacement for the following reasons:
1.  **Cross-Platform Support:** `pdfx` officially supports Windows, macOS, Linux, Web, Android, and iOS, resolving the immediate build issue.
2.  **Cost-Effective:** It is an open-source and free package, avoiding the licensing costs associated with commercial alternatives like `syncfusion_flutter_pdfviewer`.
3.  **Sufficient Functionality:** For the project's core requirement of rendering and displaying PDF pages, `pdfx` provides all necessary functionalities without the overhead of advanced features (like editing or annotations) that are not needed.

**Implementation Details:**
*   The `pubspec.yaml` file will be updated to remove `flutter_pdfview` and add the `pdfx` dependency.
*   The `PdfViewWrapper` widget will be refactored to use the `PdfView` and `PdfController` from the `pdfx` package.
*   The `memory-bank/systemPatterns.md` document will be updated to reflect this change in the technology stack.

---
### Decision (Debug)
[2025-06-30 15:56:33] - Bug Fix Strategy: Fix PDF scaling and scrolling logic.

**Rationale:**
The application had two critical bugs: PDF pages were stretched, and auto-scrolling was broken.
1.  **Scaling Issue:** The `pdfx` viewer was not configured correctly, causing pages to stretch to fill the view instead of maintaining their aspect ratio. The fix was to use the `PdfViewBuilders` with a `pageBuilder` that renders each page within a `PhotoView`, which correctly handles scaling (`BoxFit.contain`).
2.  **Scrolling Issue:** The previous implementation used a manual `ScrollController`, which is incompatible with the `pdfx` plugin's internal controller. The fix was to remove the manual `ScrollController` and instead use the `PdfController`'s `animateToPage()` method, driven by a `Timer`, to achieve smooth, time-based page transitions.

**Details:**
*   **Affected components/files:**
    *   `flutter_pdf_viewer/lib/widgets/pdf_view_wrapper.dart`: Refactored to remove `ScrollController` and local `PdfController` state. It now accepts a `PdfController` from its parent and uses `PdfViewBuilders` with a `PhotoView`-based `pageBuilder` to ensure correct page scaling.
    *   `flutter_pdf_viewer/lib/widgets/pdf_player.dart`: Removed the manual `ScrollController`. It now creates and manages a `PdfController` for each PDF. The scrolling logic was completely replaced with `_startPageAnimation`, which uses a `Timer` to call `_pdfController.animateToPage()` periodically.
---
### Decision
[2025-06-30 16:35:20] - WPF PDF Viewer: Project Structure and Core Technology Stack

**Rationale:**
To build a maintainable and scalable WPF application, the Model-View-ViewModel (MVVM) pattern was chosen. This separation of concerns is standard for WPF development and facilitates testing and UI/logic independence. The project structure reflects this pattern with dedicated folders for `Views`, `ViewModels`, and `Models`. A `Services` folder is included to encapsulate external dependencies and logic, such as PDF rendering, keeping the ViewModels clean.

**Implications/Details:**
*   **Project Name:** `WpfPdfViewer`
*   **Core Pattern:** MVVM
*   **Folder Structure:**
    *   `WpfPdfViewer/`
        *   `Views/` (XAML files, UI)
        *   `ViewModels/` (UI logic, state management)
        *   `Models/` (Data structures)
        *   `Services/` (PDF rendering, file I/O)
*   **PDF Rendering Library:** A third-party library will be required. The selection of a specific library (e.g., PdfiumViewer, CefSharp) is deferred, but the architecture accommodates it within the `Services` layer.
- **2025-06-30 17:55:05**: Decided to explicitly include `x86/pdfium.dll` and `x64/pdfium.dll` in `WpfPdfViewer.csproj` to resolve runtime loading issues in the Release build. This was necessary because the `PdfiumViewer` package alone did not guarantee the native DLLs would be present in the output directory.
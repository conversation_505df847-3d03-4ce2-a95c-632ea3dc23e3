# Product Context

This file provides a high-level overview of the project and the expected product that will be created. Initially it is based upon projectBrief.md (if provided) and all other available project-related information in the working directory. This file is intended to be updated as the project evolves, and should be used to inform all other modes of the project's goals and context.
2025-06-30 10:55:31 - Log of updates made will be appended as footnotes to the end of this file.

*

## Project Goal

*   创建一个 Electron 应用程序，用于自动循环播放指定文件夹中的 PDF 文件。应用程序应全屏显示，并能自动滚动浏览每个 PDF 的所有页面，然后无缝切换到下一个 PDF 文件。

## Key Features

*   从 `config.json` 读取 PDF 文件夹路径。
*   全屏显示 PDF 内容。
*   自动垂直滚动浏览 PDF 页面。
*   在单个 PDF 的所有页面滚动完毕后，自动切换到下一个 PDF。
*   循环播放文件夹中的所有 PDF。

## Overall Architecture

*   **主进程 (`main.js`):** 负责创建浏览器窗口、读取配置文件 (`config.json`)、访问文件系统以获取 PDF 文件列表，并通过 IPC 将文件列表发送到渲染器进程。
*   **渲染器进程 (`renderer.js`):** 在 `index.html` 中运行，负责接收 PDF 文件列表，使用 PDF.js 库加载和渲染 PDF，并处理自动滚动和文件切换逻辑。
*   **视图 (`index.html` & `styles.css`):** 提供显示 PDF 的基本 HTML 结构和样式。
*   **配置 (`config.json`):** 存储 PDF 文件所在的文件夹路径，方便用户配置。
*   **依赖管理 (`package.json`):** 定义项目元数据和依赖项（如 Electron）。

---

## Project: WPF PDF Viewer

### Project Goal

*   Create a native Windows Presentation Foundation (WPF) application for viewing local PDF files.

### Key Features

*   Open a local PDF file via a standard file selection dialog.
*   Render and display the selected PDF within the application window.
*   Navigate between pages using "Previous" and "Next" buttons.
*   Display the current page number and the total number of pages.

### Overall Architecture

*   **View (XAML):** Defines the user interface, including the PDF display area, navigation buttons, and page count display.
*   **ViewModel (C#):** Manages the application's state and logic. It handles file opening, communicates with the PDF rendering library, and exposes properties for data binding (e.g., current page, total pages, PDF document).
*   **Model (C#):** Represents the core data, primarily the PDF document itself.
*   **PDF Library:** A third-party library (e.g., PdfiumViewer) responsible for rendering PDF documents.

*Timestamp: 2025-06-30 16:31:00*
# System Patterns

This file documents recurring patterns and standards used in the project.
It is optional, but recommended to be updated as the project evolves.
2025-06-30 14:53:00 - Log of updates made.

*

## Architectural Patterns

### Flutter Application Architecture for PDF Auto-Player

This document outlines the high-level architecture for the Flutter version of the PDF Auto-Player application. It translates the core logic from the previous Electron implementation into a modern, scalable Flutter structure.

#### 1. Technology Stack & Key Plugins

*   **UI Framework**: Flutter
*   **State Management**: `riverpod` & `flutter_riverpod`
    *   **Rationale**: Provides a compile-safe, scalable, and declarative way to manage application state, making it easy to handle dependencies between different parts of the app (like configuration and file lists). It's a modern replacement for `Provider`.
*   **PDF Rendering**: `pdfx`
    *   **Rationale**: A cross-platform PDF rendering plugin that supports Windows, macOS, Linux, Web, Android, and iOS. It was chosen to replace `flutter_pdfview` due to the latter's lack of Windows support, which is a critical project requirement. `pdfx` provides the necessary rendering capabilities while ensuring the application can be built and deployed on the target Windows platform.
*   **Filesystem Access**: `dart:io`, `path_provider`, `permission_handler`
    *   **Rationale**: `dart:io` provides the core file system APIs. `path_provider` is used to find standard locations on the filesystem. `permission_handler` is crucial for requesting storage permissions on mobile platforms.
*   **Configuration**: `dart:convert` for JSON parsing.
    *   **Rationale**: To maintain consistency with the Electron app's `config.json`, the app will read a JSON file from a designated application directory.

#### 2. Component / Widget Structure

The application will be structured with a clear separation of concerns, from UI presentation to business logic.

```mermaid
graph TD
    A[main.dart] --> B(App Root);
    B --> C{ProviderScope};
    C --> D[HomePage];

    subgraph HomePage
        D --> E{FutureBuilder/Consumer};
        E -- Loading --> F[LoadingIndicator];
        E -- Error --> G[ErrorMessage];
        E -- Data --> H[PdfPlayer];
    end

    subgraph PdfPlayer
        H --> I[PdfViewWrapper];
        H --> J[Controls Overlay - Future];
    end

    subgraph Services
        K[ConfigService]
        L[PdfFileService]
        M[PlayerStateNotifier]
    end

    style Services fill:#f9f,stroke:#333,stroke-width:2px
```

*   **`main.dart`**: Application entry point. Initializes the `ProviderScope` for Riverpod.
*   **`HomePage`**: The main screen. It uses a `ConsumerWidget` to watch providers and orchestrate the UI based on the application's state (e.g., loading config, loading PDFs, showing errors, or displaying the player).
*   **`PdfPlayer`**: A core `ConsumerWidget` that listens to the `playerStateProvider`. It manages the playback loop, including the auto-scrolling logic and timers for switching to the next PDF.
*   **`PdfViewWrapper`**: A wrapper around the `pdfx`'s `PdfView` widget. It takes a file path, renders the PDF using a `PdfController`, and helps manage the PDF document lifecycle.
*   **`ConfigService`**: A plain Dart class responsible for finding, reading, and parsing the `config.json` file.
*   **`PdfFileService`**: A plain Dart class that takes a directory path and returns a list of PDF files (`List<File>`).
*   **`PlayerStateNotifier`**: The heart of the state management. A `StateNotifier` that holds the application's state, such as the list of PDF files, the current PDF index, playback status, and any error messages. It exposes methods to manipulate this state (e.g., `loadNextPdf()`).

#### 3. Data Flow and State Management (with Riverpod)

The data flow is unidirectional, ensuring a predictable and maintainable state.

1.  **Initialization**: `HomePage` requests data from `pdfListProvider`.
2.  **Dependency Chain**: `pdfListProvider` depends on `configProvider`. Riverpod automatically manages this, first triggering `configProvider`.
3.  **Configuration Loading**: `configProvider` uses `ConfigService` to load `config.json`.
4.  **File Loading**: Once the config is available, `pdfListProvider` uses the folder path from the config and the `PdfFileService` to get the list of PDFs.
5.  **State Update**: The loaded list of PDFs is passed to the `PlayerStateNotifier`.
6.  **UI Reaction**:
    *   `HomePage` listens to the providers and shows a loading spinner, an error message, or the `PdfPlayer` widget.
    *   `PdfPlayer` listens to `playerStateProvider`. When the `currentFileIndex` changes, it rebuilds and passes the new file path to `PdfViewWrapper`.
7.  **Playback Loop**:
    *   `PdfPlayer` contains the scrolling logic (e.g., using a `Ticker` or `Timer`).
    *   When a PDF scrolls to the end, `PdfPlayer` calls a method on the `PlayerStateNotifier` (e.g., `loadNextPdf()`).
    *   This updates the state, which in turn causes the UI to rebuild with the next PDF, thus completing the loop.

**Riverpod Providers Example:**

```dart
// Manages configuration loading
final configProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  final configService = ConfigService();
  return await configService.readConfig();
});

// Manages the list of PDF files, depends on the config
final pdfListProvider = FutureProvider<List<File>>((ref) async {
  final config = await ref.watch(configProvider.future);
  final pdfFolderPath = config['pdfFolderPath'];
  final pdfFileService = PdfFileService();
  return await pdfFileService.getPdfFiles(pdfFolderPath);
});

// Manages the active playback state
final playerStateProvider = StateNotifierProvider<PlayerStateNotifier, PlayerState>((ref) {
  return PlayerStateNotifier();
});
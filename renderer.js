const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

contextBridge.exposeInMainWorld('electronAPI', {
  onPdfFiles: (callback) => ipcRenderer.on('pdf-files', callback),
  getPdfFiles: () => ipcRenderer.invoke('get-pdf-files')
});

window.addEventListener('DOMContentLoaded', () => {
  // 设置 PDF.js worker 的源（使用离线版本）
  pdfjsLib.GlobalWorkerOptions.workerSrc = 'lib/pdf.worker.min.js';

  const pdfContainer = document.getElementById('pdf-container');
  let pdfFiles = [];
  let currentPdfIndex = 0;
  let currentPage = 1;
  let currentPdf = null;
  let scrollInterval;
  const SCROLL_DELAY = 50; // 每次滚动的延迟（毫秒）
  const PAGE_CHANGE_DELAY = 5000; // 切换到下一页的延迟（毫秒）

  // 函数：渲染指定 PDF 的特定页面
  async function renderPage(pdf, pageNum) {
    const page = await pdf.getPage(pageNum);
    const viewport = page.getViewport({ scale: 1.5 });
    
    const canvas = document.createElement('canvas');
    const context = canvas.getContext('2d');
    canvas.height = viewport.height;
    canvas.width = viewport.width;

    // 清空容器并添加新的 canvas
    pdfContainer.innerHTML = '';
    pdfContainer.appendChild(canvas);
    
    // 渲染页面内容
    const renderContext = {
      canvasContext: context,
      viewport: viewport
    };
    await page.render(renderContext).promise;
    
    // 重置滚动位置并开始自动滚动
    pdfContainer.scrollTop = 0;
    startScrolling(canvas.height);
  }

  // 函数：开始自动滚动
  function startScrolling(canvasHeight) {
    clearInterval(scrollInterval); // 清除之前的滚动
    let scrollPosition = 0;
    scrollInterval = setInterval(() => {
      scrollPosition += 1; // 每次滚动的像素数
      pdfContainer.scrollTop = scrollPosition;
      // 如果滚动到底部，则清除间隔并准备切换到下一页
      if (scrollPosition >= canvasHeight - pdfContainer.clientHeight) {
        clearInterval(scrollInterval);
        setTimeout(nextPage, PAGE_CHANGE_DELAY);
      }
    }, SCROLL_DELAY);
  }

  // 函数：切换到下一页或下一个 PDF
  async function nextPage() {
    currentPage++;
    if (currentPdf && currentPage <= currentPdf.numPages) {
      await renderPage(currentPdf, currentPage);
    } else {
      // 移动到下一个 PDF
      currentPdfIndex = (currentPdfIndex + 1) % pdfFiles.length;
      await loadPdf(pdfFiles[currentPdfIndex]);
    }
  }

  // 函数：加载并显示 PDF
  async function loadPdf(pdfPath) {
    try {
      console.log(`正在尝试加载文件: ${pdfPath}`);
      // 注意：pdf.js 在浏览器环境中通常需要 URL 或 Uint8Array。
      // 在 Electron 渲染器中直接使用文件路径可能需要调整，
      // 例如通过主进程读取文件内容并发送到渲染器。
      // 这里我们假设 getDocument 可以处理 file:// 协议的路径
      const loadingTask = pdfjsLib.getDocument(pdfPath);
      currentPdf = await loadingTask.promise;
      currentPage = 1;
      await renderPage(currentPdf, currentPage);
    } catch (error) {
      console.error(`加载 PDF 时出错: ${pdfPath}`, error);
      pdfContainer.innerHTML = `
        <h1>加载文件失败</h1>
        <p>文件: ${pdfPath}</p>
        <p>将在5秒后尝试下一个文件...</p>
      `;
      // 如果加载失败，则在延迟后尝试加载下一个 PDF
      setTimeout(nextPage, 5000);
    }
  }

  // 监听来自主进程的 PDF 文件列表
  window.electronAPI.onPdfFiles((event, files) => {
    console.log('收到PDF文件列表:', files);
    if (files && files.length > 0) {
      pdfFiles = files;
      currentPdfIndex = 0;
      loadPdf(pdfFiles[currentPdfIndex]);
    } else {
      pdfContainer.innerHTML = `
        <h1>错误：未找到 PDF 文件</h1>
        <p>请检查 config.json 中的 'pdfFolderPath' 配置是否正确，并确保目标文件夹中包含有效的 PDF 文件。</p>
        <p>当前配置的路径：请查看控制台输出</p>
      `;
    }
  });

  // 添加初始化时的调试信息
  console.log('PDF查看器已初始化，等待PDF文件列表...');
});